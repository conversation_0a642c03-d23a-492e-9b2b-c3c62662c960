"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/workbench/page",{

/***/ "(app-pages-browser)/./app/workbench/components/ClassDetail.tsx":
/*!**************************************************!*\
  !*** ./app/workbench/components/ClassDetail.tsx ***!
  \**************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Blocks_ChevronDown_Download_Edit_Gift_Link_Plus_Search_Settings_Trash2_Upload_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Blocks,ChevronDown,Download,Edit,Gift,Link,Plus,Search,Settings,Trash2,Upload,UserPlus,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Blocks_ChevronDown_Download_Edit_Gift_Link_Plus_Search_Settings_Trash2_Upload_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Blocks,ChevronDown,Download,Edit,Gift,Link,Plus,Search,Settings,Trash2,Upload,UserPlus,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Blocks_ChevronDown_Download_Edit_Gift_Link_Plus_Search_Settings_Trash2_Upload_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Blocks,ChevronDown,Download,Edit,Gift,Link,Plus,Search,Settings,Trash2,Upload,UserPlus,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Blocks_ChevronDown_Download_Edit_Gift_Link_Plus_Search_Settings_Trash2_Upload_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Blocks,ChevronDown,Download,Edit,Gift,Link,Plus,Search,Settings,Trash2,Upload,UserPlus,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user-plus.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Blocks_ChevronDown_Download_Edit_Gift_Link_Plus_Search_Settings_Trash2_Upload_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Blocks,ChevronDown,Download,Edit,Gift,Link,Plus,Search,Settings,Trash2,Upload,UserPlus,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Blocks_ChevronDown_Download_Edit_Gift_Link_Plus_Search_Settings_Trash2_Upload_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Blocks,ChevronDown,Download,Edit,Gift,Link,Plus,Search,Settings,Trash2,Upload,UserPlus,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Blocks_ChevronDown_Download_Edit_Gift_Link_Plus_Search_Settings_Trash2_Upload_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Blocks,ChevronDown,Download,Edit,Gift,Link,Plus,Search,Settings,Trash2,Upload,UserPlus,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Blocks_ChevronDown_Download_Edit_Gift_Link_Plus_Search_Settings_Trash2_Upload_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Blocks,ChevronDown,Download,Edit,Gift,Link,Plus,Search,Settings,Trash2,Upload,UserPlus,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/link.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Blocks_ChevronDown_Download_Edit_Gift_Link_Plus_Search_Settings_Trash2_Upload_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Blocks,ChevronDown,Download,Edit,Gift,Link,Plus,Search,Settings,Trash2,Upload,UserPlus,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/gift.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Blocks_ChevronDown_Download_Edit_Gift_Link_Plus_Search_Settings_Trash2_Upload_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Blocks,ChevronDown,Download,Edit,Gift,Link,Plus,Search,Settings,Trash2,Upload,UserPlus,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/blocks.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Blocks_ChevronDown_Download_Edit_Gift_Link_Plus_Search_Settings_Trash2_Upload_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Blocks,ChevronDown,Download,Edit,Gift,Link,Plus,Search,Settings,Trash2,Upload,UserPlus,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Blocks_ChevronDown_Download_Edit_Gift_Link_Plus_Search_Settings_Trash2_Upload_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Blocks,ChevronDown,Download,Edit,Gift,Link,Plus,Search,Settings,Trash2,Upload,UserPlus,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Blocks_ChevronDown_Download_Edit_Gift_Link_Plus_Search_Settings_Trash2_Upload_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Blocks,ChevronDown,Download,Edit,Gift,Link,Plus,Search,Settings,Trash2,Upload,UserPlus,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Blocks_ChevronDown_Download_Edit_Gift_Link_Plus_Search_Settings_Trash2_Upload_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Blocks,ChevronDown,Download,Edit,Gift,Link,Plus,Search,Settings,Trash2,Upload,UserPlus,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Blocks_ChevronDown_Download_Edit_Gift_Link_Plus_Search_Settings_Trash2_Upload_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Blocks,ChevronDown,Download,Edit,Gift,Link,Plus,Search,Settings,Trash2,Upload,UserPlus,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _lib_api_class__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../lib/api/class */ \"(app-pages-browser)/./lib/api/class.ts\");\n/* harmony import */ var _lib_api_student__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../lib/api/student */ \"(app-pages-browser)/./lib/api/student.ts\");\n/* harmony import */ var react_redux__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! react-redux */ \"(app-pages-browser)/./node_modules/react-redux/dist/react-redux.mjs\");\n/* harmony import */ var _contexts_TemplateContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../contexts/TemplateContext */ \"(app-pages-browser)/./app/workbench/contexts/TemplateContext.tsx\");\n/* harmony import */ var _AddStudentModal__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./AddStudentModal */ \"(app-pages-browser)/./app/workbench/components/AddStudentModal.tsx\");\n/* harmony import */ var _teacher_space_components_modals__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../teacher-space/components/modals */ \"(app-pages-browser)/./app/teacher-space/components/modals/index.ts\");\n/* harmony import */ var _AssignBlocksModal__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./AssignBlocksModal */ \"(app-pages-browser)/./app/workbench/components/AssignBlocksModal.tsx\");\n/* harmony import */ var _TransferClassModal__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./TransferClassModal */ \"(app-pages-browser)/./app/workbench/components/TransferClassModal.tsx\");\n/* harmony import */ var _AssignPointsModal__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./AssignPointsModal */ \"(app-pages-browser)/./app/workbench/components/AssignPointsModal.tsx\");\n/* harmony import */ var _BatchUseKeyPackageModal__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./BatchUseKeyPackageModal */ \"(app-pages-browser)/./app/workbench/components/BatchUseKeyPackageModal.tsx\");\n/* harmony import */ var _StudentList__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./StudentList */ \"(app-pages-browser)/./app/workbench/components/StudentList/index.tsx\");\n/* harmony import */ var _ClassDetail_css__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./ClassDetail.css */ \"(app-pages-browser)/./app/workbench/components/ClassDetail.css\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// 类型定义已移至 ./types/index.ts\nconst ClassDetail = (param)=>{\n    let { classInfo: initialClassInfo, selectedSchool, onBack, onClassInfoUpdate, onClassDeleted } = param;\n    var _selectedStudent_nickName;\n    _s();\n    // 创建内部状态来管理班级信息，这样可以在编辑后更新显示\n    const [classInfo, setClassInfo] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(initialClassInfo);\n    const [students, setStudents] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [selectedStudent, setSelectedStudent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // 同步外部传入的classInfo变化\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setClassInfo(initialClassInfo);\n    }, [\n        initialClassInfo\n    ]);\n    const [isAddStudentModalVisible, setIsAddStudentModalVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isSettingsDropdownOpen, setIsSettingsDropdownOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isBatchActionsDropdownOpen, setIsBatchActionsDropdownOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const dropdownRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const settingsDropdownRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const batchActionsDropdownRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // 模态框状态\n    const [isEditClassModalVisible, setIsEditClassModalVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isImportStudentModalVisible, setIsImportStudentModalVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isTransferClassModalVisible, setIsTransferClassModalVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isInviteCodeModalVisible, setIsInviteCodeModalVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isAssignBlocksModalVisible, setIsAssignBlocksModalVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [inviteCode, setInviteCode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    // 新增功能模态框状态\n    const [isPublishTaskModalVisible, setIsPublishTaskModalVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isResetPasswordModalVisible, setIsResetPasswordModalVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isAssignPointsModalVisible, setIsAssignPointsModalVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isBatchUseKeyPackageModalVisible, setIsBatchUseKeyPackageModalVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // const [isRedeemKeyModalVisible, setIsRedeemKeyModalVisible] = useState(false); // 未使用，已移除\n    // 转让管理相关状态\n    const [searchedTeacher, setSearchedTeacher] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [transferLoading, setTransferLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // PublishTaskModal 相关状态\n    const [fileList, setFileList] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [uploading, setUploading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [templates, setTemplates] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [officialTemplates] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]); // 保留以避免引用错误\n    const [selectedTemplateId, setSelectedTemplateId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // AssignBlocksModal 相关状态\n    const [loadingTemplates, setLoadingTemplates] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [studentTemplateUsage, setStudentTemplateUsage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [teacherTemplate, setTeacherTemplate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    const [selectedStudentId, setSelectedStudentId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // 添加userRoles状态，与teacher-space保持一致\n    const [userRoles, setUserRoles] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // 多选状态\n    const [selectedStudentIds, setSelectedStudentIds] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isSelectAll, setIsSelectAll] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // 使用全局模板状态\n    const { currentTemplate, globalTemplateChangeVersion, refreshCurrentTemplate } = (0,_contexts_TemplateContext__WEBPACK_IMPORTED_MODULE_4__.useTemplate)();\n    // 全局当前模板信息（用于没有个人模板的学生）\n    const [globalCurrentTemplate, setGlobalCurrentTemplate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // 获取教师当前模板（从班级接口获取）\n    const fetchTeacherCurrentTemplate = async ()=>{\n        try {\n            if (!(classInfo === null || classInfo === void 0 ? void 0 : classInfo.id) || !(classInfo === null || classInfo === void 0 ? void 0 : classInfo.schoolId)) return null;\n            const response = await _lib_api_class__WEBPACK_IMPORTED_MODULE_2__.classApi.getTeacherClasses(classInfo.schoolId, userId);\n            if (response.data.code === 200 && response.data.data) {\n                // 找到当前班级的模板信息\n                const currentClass = response.data.data.find((cls)=>cls.id === classInfo.id);\n                if (currentClass && currentClass.templateName) {\n                    const templateInfo = {\n                        templateId: currentClass.templateId || 0,\n                        templateName: currentClass.templateName,\n                        isOfficial: currentClass.isOfficial || false\n                    };\n                    setGlobalCurrentTemplate(templateInfo);\n                    console.log(\"获取到教师当前模板:\", templateInfo);\n                    return templateInfo;\n                }\n            }\n        } catch (error) {\n            console.error(\"获取教师当前模板失败:\", error);\n        }\n        return null;\n    };\n    // 同步教师模板到学生（当教师模板更新时调用）\n    const syncTeacherTemplateToStudents = async ()=>{\n        try {\n            const newTemplate = await fetchTeacherCurrentTemplate();\n            if (newTemplate && students.length > 0) {\n                console.log(\"同步教师模板到学生:\", newTemplate);\n                // 使用 ref 获取最新的 personalTemplateAssignments\n                const currentPersonalAssignments = personalTemplateAssignmentsRef.current;\n                // 更新所有没有个人分配模板的学生\n                setStudents((prevStudents)=>prevStudents.map((student)=>{\n                        // 如果学生有个人分配的模板，保持不变\n                        if (currentPersonalAssignments.has(student.userId)) {\n                            console.log(\"保持学生 \".concat(student.nickName, \" 的个人模板 (syncTeacherTemplateToStudents)\"));\n                            return student;\n                        }\n                        // 否则更新为教师当前模板\n                        console.log(\"更新学生 \".concat(student.nickName, \" 为教师模板 (syncTeacherTemplateToStudents)\"));\n                        return {\n                            ...student,\n                            currentTemplate: newTemplate\n                        };\n                    }));\n            }\n        } catch (error) {\n            console.error(\"同步教师模板失败:\", error);\n        }\n    };\n    // 存储个人分配的模板信息，避免被fetchStudents覆盖\n    const [personalTemplateAssignments, setPersonalTemplateAssignments] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Map());\n    // 添加强制重新渲染的状态\n    const [renderVersion] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0); // setRenderVersion暂时未使用\n    // 获取用户信息\n    const userId = (0,react_redux__WEBPACK_IMPORTED_MODULE_13__.useSelector)((state)=>state.user.userState.userId);\n    const roleId = (0,react_redux__WEBPACK_IMPORTED_MODULE_13__.useSelector)((state)=>state.user.userState.roleId);\n    // 获取班级学生数据\n    const fetchStudents = async ()=>{\n        if (!(classInfo === null || classInfo === void 0 ? void 0 : classInfo.id)) return;\n        try {\n            setLoading(true);\n            setError(null);\n            // 并行获取班级学生基础数据和教师当前模板\n            const [response] = await Promise.all([\n                _lib_api_class__WEBPACK_IMPORTED_MODULE_2__.classApi.getClassStudents(classInfo.id),\n                fetchTeacherCurrentTemplate()\n            ]);\n            if (response.data.code === 200) {\n                const studentsData = response.data.data || [];\n                if (studentsData.length === 0) {\n                    setStudents([]);\n                    setSelectedStudent(null);\n                    // 如果学生数量变为0，也要更新班级信息\n                    if (classInfo.studentCount !== 0) {\n                        const updatedClassInfo = {\n                            ...classInfo,\n                            studentCount: 0\n                        };\n                        setClassInfo(updatedClassInfo);\n                        // 通知父组件更新班级列表中的学生数\n                        onClassInfoUpdate === null || onClassInfoUpdate === void 0 ? void 0 : onClassInfoUpdate(updatedClassInfo);\n                    }\n                    return;\n                }\n                // 提取学生ID列表\n                const userIds = studentsData.map((s)=>s.userId);\n                // 批量获取学生详细信息（包括模板和能量信息）\n                const studentInfoMap = await (userIds.length > 20 ? Promise.all(Array.from({\n                    length: Math.ceil(userIds.length / 20)\n                }, (_, i)=>userIds.slice(i * 20, (i + 1) * 20)).map((batchId)=>_lib_api_student__WEBPACK_IMPORTED_MODULE_3__.studentApi.getStudentsBatchInfo(batchId).then((param)=>{\n                        let { data: { data } } = param;\n                        return data;\n                    }).catch((error)=>{\n                        console.error(\"获取学生批量信息失败:\", error);\n                        return {};\n                    }))).then((results)=>Object.assign({}, ...results)) : _lib_api_student__WEBPACK_IMPORTED_MODULE_3__.studentApi.getStudentsBatchInfo(userIds).then((param)=>{\n                    let { data: { data } } = param;\n                    return data;\n                }).catch((error)=>{\n                    console.error(\"获取学生批量信息失败:\", error);\n                    return {};\n                }));\n                // 合并学生数据\n                const completeStudents = studentsData.map((s)=>{\n                    const studentInfo = studentInfoMap[s.userId];\n                    // 检查是否有个人分配的模板\n                    const personalTemplate = personalTemplateAssignments.get(s.userId);\n                    return {\n                        ...s,\n                        ...studentInfo,\n                        id: s.userId,\n                        nickName: s.nickName || \"学生\".concat(s.studentNumber || s.userId),\n                        totalPoints: (studentInfo === null || studentInfo === void 0 ? void 0 : studentInfo.totalPoints) || 0,\n                        availablePoints: (studentInfo === null || studentInfo === void 0 ? void 0 : studentInfo.availablePoints) || 0,\n                        avatarUrl: s.avatarUrl || \"/default-avatar.png\",\n                        // 优先级：个人分配的模板 > 学生API返回的模板 > 教师当前模板 > null\n                        currentTemplate: (()=>{\n                            var _studentsData_;\n                            const result = personalTemplate || (studentInfo === null || studentInfo === void 0 ? void 0 : studentInfo.currentTemplate) || globalCurrentTemplate;\n                            // 调试信息\n                            if (s.userId === ((_studentsData_ = studentsData[0]) === null || _studentsData_ === void 0 ? void 0 : _studentsData_.userId)) {\n                                console.log(\"学生模板分配逻辑:\", {\n                                    studentId: s.userId,\n                                    studentName: s.nickName,\n                                    personalTemplate,\n                                    studentApiTemplate: studentInfo === null || studentInfo === void 0 ? void 0 : studentInfo.currentTemplate,\n                                    globalCurrentTemplate,\n                                    finalTemplate: result\n                                });\n                            }\n                            return result;\n                        })()\n                    };\n                });\n                setStudents(completeStudents);\n                // 如果学生数量发生变化，更新班级信息并通知父组件\n                if (completeStudents.length !== classInfo.studentCount) {\n                    const updatedClassInfo = {\n                        ...classInfo,\n                        studentCount: completeStudents.length\n                    };\n                    setClassInfo(updatedClassInfo);\n                    // 通知父组件更新班级列表中的学生数\n                    onClassInfoUpdate === null || onClassInfoUpdate === void 0 ? void 0 : onClassInfoUpdate(updatedClassInfo);\n                }\n                // 设置userRoles，与teacher-space保持一致\n                const newUserRoles = completeStudents.map((student)=>({\n                        userId: student.userId,\n                        roleId: 1 // 学生角色ID为1\n                    }));\n                // 添加教师自己的角色\n                newUserRoles.push({\n                    userId: userId,\n                    roleId: 2 // 教师角色ID为2\n                });\n                setUserRoles(newUserRoles);\n                // 默认选择第一个学生\n                if (completeStudents.length > 0) {\n                    setSelectedStudent(completeStudents[0]);\n                }\n            } else {\n                setError(response.data.message || \"获取学生列表失败\");\n            }\n        } catch (err) {\n            console.error(\"获取学生列表失败:\", err);\n            console.error(\"错误详情:\", {\n                message: err instanceof Error ? err.message : \"未知错误\",\n                stack: err instanceof Error ? err.stack : undefined,\n                classId: classInfo.id\n            });\n            setError(\"获取学生列表失败，请稍后重试\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    // 获取权限模板列表\n    const fetchTemplates = async ()=>{\n        setLoadingTemplates(true);\n        try {\n            const { getRoleTemplateList, getOfficialTemplates } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! ../../../lib/api/role */ \"(app-pages-browser)/./lib/api/role.ts\"));\n            // 同时获取教师自定义模板和官方模板\n            const [customResponse, officialResponse] = await Promise.all([\n                getRoleTemplateList(userId),\n                getOfficialTemplates()\n            ]);\n            if (customResponse.data.code === 200 && officialResponse.data.code === 200) {\n                const customTemplates = customResponse.data.data || [];\n                const officialTemplates = officialResponse.data.data || [];\n                // 为官方模板添加标记\n                const markedOfficialTemplates = officialTemplates.map((template)=>({\n                        ...template,\n                        isOfficial: true\n                    }));\n                // 合并所有模板\n                const allTemplates = [\n                    ...customTemplates,\n                    ...markedOfficialTemplates\n                ];\n                setTemplates(allTemplates);\n            }\n        } catch (error) {\n            console.error(\"获取权限模板列表失败:\", error);\n        } finally{\n            setLoadingTemplates(false);\n        }\n    };\n    // 获取模板使用情况\n    const fetchTemplateUsage = async ()=>{\n        try {\n            const { getUserCurrentTemplate, getStudentTemplates } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! ../../../lib/api/role */ \"(app-pages-browser)/./lib/api/role.ts\"));\n            // 获取教师使用的模板\n            const teacherResponse = await getUserCurrentTemplate(userId);\n            if (teacherResponse.data.code === 200) {\n                const teacherTemplateData = teacherResponse.data.data;\n                setTeacherTemplate(teacherTemplateData);\n            // 同时更新当前模板状态，确保与模板管理同步\n            // 暂时注释掉以停止循环\n            /*\r\n        if (teacherTemplateData) {\r\n          // 使用refreshCurrentTemplate来更新全局模板状态\r\n          await refreshCurrentTemplate();\r\n        }\r\n        */ }\n            // 获取学生使用的模板\n            const studentResponse = await getStudentTemplates({\n                teacherId: userId,\n                page: 1,\n                size: 200\n            });\n            if (studentResponse.data.code === 200) {\n                // 统计每个模板被使用的次数\n                const usage = {};\n                studentResponse.data.data.list.forEach((item)=>{\n                    if (item.templateId) {\n                        usage[item.templateId] = (usage[item.templateId] || 0) + 1;\n                    }\n                });\n                setStudentTemplateUsage(usage);\n            }\n        } catch (error) {\n            console.error(\"获取模板使用情况失败:\", error);\n        }\n    };\n    // 组件挂载时获取学生数据和模板\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        fetchStudents();\n        fetchTemplates();\n        fetchTemplateUsage();\n    }, [\n        classInfo === null || classInfo === void 0 ? void 0 : classInfo.id,\n        userId\n    ]);\n    // 当组件重新挂载或班级变化时，确保获取最新的当前模板\n    // 暂时注释掉以停止循环\n    /*\r\n  useEffect(() => {\r\n    if (classInfo?.id && userId) {\r\n      refreshCurrentTemplate();\r\n    }\r\n  }, [classInfo?.id, userId]);\r\n  */ // 移除这个useEffect，避免在currentTemplate变化时覆盖个人分配的模板\n    // 使用 useRef 来保存最新的 personalTemplateAssignments\n    const personalTemplateAssignmentsRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(personalTemplateAssignments);\n    personalTemplateAssignmentsRef.current = personalTemplateAssignments;\n    // 监听全局模板变化，重新获取教师当前模板并更新学生数据\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (globalTemplateChangeVersion > 0 && (classInfo === null || classInfo === void 0 ? void 0 : classInfo.id)) {\n            console.log(\"检测到模板变化，重新获取教师当前模板\");\n            // 重新获取教师当前模板\n            fetchTeacherCurrentTemplate().then((newTemplate)=>{\n                if (newTemplate && students.length > 0) {\n                    // 不清除个人分配的模板记录，保留学生的个人模板数据\n                    // setPersonalTemplateAssignments(new Map()); // 注释掉这行\n                    // 使用 ref 获取最新的 personalTemplateAssignments\n                    const currentPersonalAssignments = personalTemplateAssignmentsRef.current;\n                    // 只更新没有个人模板的学生为新的教师当前模板\n                    console.log(\"全局模板变化，开始更新学生模板:\", {\n                        newTemplate,\n                        personalTemplateAssignments: Array.from(currentPersonalAssignments.entries()),\n                        studentsCount: students.length\n                    });\n                    setStudents((prevStudents)=>prevStudents.map((student)=>{\n                            const hasPersonalTemplate = currentPersonalAssignments.has(student.userId);\n                            console.log(\"学生 \".concat(student.nickName, \" (\").concat(student.userId, \"):\"), {\n                                hasPersonalTemplate,\n                                currentTemplate: student.currentTemplate,\n                                willUpdate: !hasPersonalTemplate\n                            });\n                            // 如果学生有个人分配的模板，保持不变\n                            if (hasPersonalTemplate) {\n                                console.log(\"保持学生 \".concat(student.nickName, \" 的个人模板\"));\n                                return student;\n                            }\n                            // 否则更新为新的教师当前模板\n                            console.log(\"更新学生 \".concat(student.nickName, \" 为教师模板\"));\n                            return {\n                                ...student,\n                                currentTemplate: newTemplate\n                            };\n                        }));\n                }\n            });\n        }\n    }, [\n        globalTemplateChangeVersion,\n        classInfo === null || classInfo === void 0 ? void 0 : classInfo.id\n    ]);\n    // 定期检查教师模板更新\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!(classInfo === null || classInfo === void 0 ? void 0 : classInfo.id)) return;\n        // 立即检查一次\n        syncTeacherTemplateToStudents();\n        // 设置定时器，每30秒检查一次模板更新\n        const interval = setInterval(()=>{\n            syncTeacherTemplateToStudents();\n        }, 30000); // 30秒\n        return ()=>clearInterval(interval);\n    }, [\n        classInfo === null || classInfo === void 0 ? void 0 : classInfo.id,\n        students.length\n    ]);\n    // 点击外部关闭下拉菜单\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const handleClickOutside = (event)=>{\n            if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {\n            // setIsMoreActionsDropdownOpen(false); // 已移除\n            }\n            if (settingsDropdownRef.current && !settingsDropdownRef.current.contains(event.target)) {\n                setIsSettingsDropdownOpen(false);\n            }\n            if (batchActionsDropdownRef.current && !batchActionsDropdownRef.current.contains(event.target)) {\n                setIsBatchActionsDropdownOpen(false);\n            }\n        };\n        document.addEventListener(\"mousedown\", handleClickOutside);\n        return ()=>{\n            document.removeEventListener(\"mousedown\", handleClickOutside);\n        };\n    }, []);\n    // 处理学生点击选择（用于右侧显示详情）\n    const handleStudentClick = (student)=>{\n        setSelectedStudent(student);\n    };\n    // 处理删除学生\n    const handleDeleteStudent = async ()=>{\n        if (!selectedStudent) {\n            alert(\"请先选择要删除的学生\");\n            return;\n        }\n        // 显示确认对话框\n        const confirmed = window.confirm(\"确定要将 \".concat(selectedStudent.nickName, \" 移出班级吗？\\n\\n此操作不可恢复！\"));\n        if (!confirmed) {\n            return;\n        }\n        try {\n            console.log(\"删除学生:\", selectedStudent);\n            // 调用删除学生的API，传入学生的userId数组\n            const response = await _lib_api_class__WEBPACK_IMPORTED_MODULE_2__.classApi.removeStudentFromClass([\n                selectedStudent.userId\n            ]);\n            console.log(\"删除学生 API 响应:\", response.data);\n            // 检查响应状态\n            if (response.data.code === 200) {\n                console.log(\"删除学生成功\");\n                alert(\"学生已成功移出班级\");\n                // 清除选中的学生\n                setSelectedStudent(null);\n                // 重新获取学生列表\n                await fetchStudents();\n            } else {\n                console.error(\"删除学生失败:\", response.data.message);\n                alert(response.data.message || \"删除学生失败\");\n            }\n        } catch (error) {\n            console.error(\"删除学生失败:\", error);\n            alert(\"删除学生失败，请稍后重试\");\n        }\n    };\n    // 编辑班级\n    const handleEditClass = async (values)=>{\n        const { GetNotification } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! logic-common/dist/components/Notification */ \"(app-pages-browser)/./node_modules/logic-common/dist/components/Notification/index.js\"));\n        const notification = GetNotification();\n        try {\n            console.log(\"开始编辑班级:\", {\n                classId: classInfo.id,\n                values: values,\n                originalClassName: classInfo.className\n            });\n            const response = await _lib_api_class__WEBPACK_IMPORTED_MODULE_2__.classApi.updateClass(classInfo.id, {\n                className: values.className,\n                grade: classInfo.grade || \"\" // 确保传递grade字段，如果没有则使用空字符串\n            });\n            console.log(\"编辑班级API响应:\", response);\n            if (response.data.code === 200) {\n                console.log(\"编辑班级成功\");\n                notification.success(\"编辑班级成功\");\n                setIsEditClassModalVisible(false);\n                // 更新本地班级信息\n                const updatedClassInfo = {\n                    ...classInfo,\n                    className: values.className\n                };\n                setClassInfo(updatedClassInfo);\n                // 通知父组件更新班级列表中的班级信息\n                onClassInfoUpdate === null || onClassInfoUpdate === void 0 ? void 0 : onClassInfoUpdate(updatedClassInfo);\n            } else {\n                console.error(\"编辑班级失败 - API返回错误:\", {\n                    code: response.data.code,\n                    message: response.data.message,\n                    data: response.data\n                });\n                notification.error(response.data.message || \"编辑班级失败\");\n                throw new Error(response.data.message || \"编辑班级失败\"); // 抛出错误让模态框知道失败了\n            }\n        } catch (error) {\n            var _error_response, _error_response1;\n            console.error(\"编辑班级失败 - 请求异常:\", {\n                error: error,\n                message: error.message,\n                response: (_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.data,\n                status: (_error_response1 = error.response) === null || _error_response1 === void 0 ? void 0 : _error_response1.status\n            });\n            // 只有在没有显示过错误消息的情况下才显示通用错误\n            if (!error.message || error.message === \"编辑班级失败\") {\n                var _error_response_data, _error_response2;\n                notification.error(((_error_response2 = error.response) === null || _error_response2 === void 0 ? void 0 : (_error_response_data = _error_response2.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || \"编辑班级失败，请稍后重试\");\n            }\n            throw error; // 重新抛出错误，让模态框保持打开状态\n        }\n    };\n    // 导入学生\n    const handleImportStudents = async (file)=>{\n        try {\n            console.log(\"导入学生文件:\", file);\n            // 这里需要实现文件解析和导入逻辑\n            alert(\"导入学生功能正在开发中\");\n            setIsImportStudentModalVisible(false);\n            return true;\n        } catch (error) {\n            console.error(\"导入学生失败:\", error);\n            alert(\"导入学生失败，请稍后重试\");\n            return false;\n        }\n    };\n    // 导出学生\n    const handleExportStudents = async ()=>{\n        try {\n            const response = await _lib_api_student__WEBPACK_IMPORTED_MODULE_3__.studentApi.exportStudents(classInfo.id);\n            console.log(\"导出学生成功:\", response);\n            alert(\"导出学生成功\");\n        } catch (error) {\n            console.error(\"导出学生失败:\", error);\n            alert(\"导出学生失败，请稍后重试\");\n        }\n    };\n    // 搜索教师\n    const handleSearchTeacher = async (phone)=>{\n        try {\n            const response = await _lib_api_class__WEBPACK_IMPORTED_MODULE_2__.classApi.searchTeacherByPhone(phone);\n            console.log(\"搜索教师响应:\", response);\n            if (response.data.code === 200) {\n                setSearchedTeacher(response.data.data);\n            } else {\n                const { GetNotification } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! logic-common/dist/components/Notification */ \"(app-pages-browser)/./node_modules/logic-common/dist/components/Notification/index.js\"));\n                const notification = GetNotification();\n                notification.error(response.data.message || \"搜索教师失败\");\n            }\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error(\"搜索教师失败:\", error);\n            const { GetNotification } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! logic-common/dist/components/Notification */ \"(app-pages-browser)/./node_modules/logic-common/dist/components/Notification/index.js\"));\n            const notification = GetNotification();\n            notification.error(((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || \"搜索教师失败\");\n        }\n    };\n    // 转让班级\n    const handleTransferClass = async (values)=>{\n        const { GetNotification } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! logic-common/dist/components/Notification */ \"(app-pages-browser)/./node_modules/logic-common/dist/components/Notification/index.js\"));\n        const notification = GetNotification();\n        setTransferLoading(true);\n        try {\n            let newTeacherId;\n            if (values.transferType === \"search\") {\n                if (!searchedTeacher) {\n                    notification.error(\"请先搜索并选择教师\");\n                    return;\n                }\n                newTeacherId = searchedTeacher.id;\n            } else {\n                // 检查是否有协助教师\n                if (!classInfo.assistantTeacherId) {\n                    notification.error(\"该班级没有协助教师\");\n                    return;\n                }\n                newTeacherId = classInfo.assistantTeacherId;\n            }\n            const response = await _lib_api_class__WEBPACK_IMPORTED_MODULE_2__.classApi.transferClass(classInfo.id, newTeacherId, values.transferType);\n            if (response.data.code === 200) {\n                notification.success(\"转让班级成功\");\n                setIsTransferClassModalVisible(false);\n                setSearchedTeacher(null);\n                // 转让成功后返回班级管理页面\n                onBack();\n            } else {\n                notification.error(response.data.message || \"转让班级失败\");\n            }\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error(\"转让班级失败:\", error);\n            notification.error(((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || \"转让班级失败\");\n        } finally{\n            setTransferLoading(false);\n        }\n    };\n    // 移出协助教师\n    const handleRemoveAssistant = async ()=>{\n        const { GetNotification } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! logic-common/dist/components/Notification */ \"(app-pages-browser)/./node_modules/logic-common/dist/components/Notification/index.js\"));\n        const notification = GetNotification();\n        try {\n            // 这里需要调用移出协助教师的API\n            // 暂时使用转让API，将assistantTeacherId设为0\n            const response = await _lib_api_class__WEBPACK_IMPORTED_MODULE_2__.classApi.updateClass(classInfo.id, {\n                assistantTeacherId: 0\n            });\n            if (response.data.code === 200) {\n                notification.success(\"移出协助教师成功\");\n                setIsTransferClassModalVisible(false);\n                // 更新本地班级信息\n                const updatedClassInfo = {\n                    ...classInfo,\n                    assistantTeacherId: 0\n                };\n                setClassInfo(updatedClassInfo);\n                onClassInfoUpdate === null || onClassInfoUpdate === void 0 ? void 0 : onClassInfoUpdate(updatedClassInfo);\n            } else {\n                notification.error(response.data.message || \"移出协助教师失败\");\n            }\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error(\"移出协助教师失败:\", error);\n            notification.error(((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || \"移出协助教师失败\");\n        }\n    };\n    // 生成邀请码\n    const handleGenerateInviteCode = async ()=>{\n        try {\n            const response = await _lib_api_class__WEBPACK_IMPORTED_MODULE_2__.classApi.generateInviteCode(classInfo.id);\n            if (response.data.code === 200) {\n                setInviteCode(response.data.data.inviteCode);\n                setIsInviteCodeModalVisible(true);\n            } else {\n                console.error(\"生成邀请码失败:\", response.data.message);\n                alert(response.data.message || \"生成邀请码失败\");\n            }\n        } catch (error) {\n            console.error(\"生成邀请码失败:\", error);\n            alert(\"生成邀请码失败，请稍后重试\");\n        }\n    };\n    // 分配积木（批量或选中学生）\n    const handleAssignBlocks = async ()=>{\n        console.log(\"=== 分配积木开始 ===\");\n        console.log(\"selectedStudentIds:\", selectedStudentIds);\n        console.log(\"selectedStudentIds.length:\", selectedStudentIds.length);\n        // 如果有选中的学生，设置为单个学生分配模式\n        if (selectedStudentIds.length === 1) {\n            console.log(\"单个学生分配模式，studentId:\", selectedStudentIds[0]);\n            setSelectedStudentId(selectedStudentIds[0]);\n        } else {\n            console.log(\"批量分配模式，学生数量:\", selectedStudentIds.length);\n            setSelectedStudentId(null);\n        }\n        await fetchTemplates();\n        await fetchTemplateUsage();\n        setIsAssignBlocksModalVisible(true);\n    };\n    // 单独为学生分配积木\n    const handleIndividualAssignBlocks = async (studentId)=>{\n        console.log(\"=== 单独为学生分配积木 ===\");\n        console.log(\"studentId:\", studentId);\n        // 设置为单个学生分配模式\n        setSelectedStudentId(studentId);\n        setSelectedStudentIds([]); // 清空批量选择\n        await fetchTemplates();\n        await fetchTemplateUsage();\n        setIsAssignBlocksModalVisible(true);\n    };\n    // 删除班级\n    const handleDeleteClass = async ()=>{\n        const { Modal } = await __webpack_require__.e(/*! import() */ \"_app-pages-browser_node_modules_antd_es_index_js\").then(__webpack_require__.bind(__webpack_require__, /*! antd */ \"(app-pages-browser)/./node_modules/antd/es/index.js\"));\n        const { GetNotification } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! logic-common/dist/components/Notification */ \"(app-pages-browser)/./node_modules/logic-common/dist/components/Notification/index.js\"));\n        const notification = GetNotification();\n        // 先检查是否有学生\n        if (students.length > 0) {\n            Modal.warning({\n                title: \"无法删除班级\",\n                centered: true,\n                content: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            children: [\n                                \"班级中还有 \",\n                                students.length,\n                                \" 名学生，请先移除所有学生后再删除班级。\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                            lineNumber: 795,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-4 text-gray-500 text-sm\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: \"删除步骤：\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                    lineNumber: 797,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ol\", {\n                                    className: \"list-decimal ml-4 mt-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"选择要移除的学生\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                            lineNumber: 799,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"使用批量操作移除所有学生\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                            lineNumber: 800,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"再次尝试删除班级\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                            lineNumber: 801,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                    lineNumber: 798,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                            lineNumber: 796,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                    lineNumber: 794,\n                    columnNumber: 11\n                }, undefined),\n                okText: \"知道了\"\n            });\n            return;\n        }\n        // 如果没有学生，显示删除确认对话框\n        Modal.confirm({\n            title: \"确认删除班级\",\n            content: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: [\n                            \"您确定要删除 \",\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                children: classInfo.className\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                lineNumber: 816,\n                                columnNumber: 21\n                            }, undefined),\n                            \" 吗？\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                        lineNumber: 816,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-3 p-3 bg-red-50 rounded-lg border border-red-200\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2 text-red-600\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-4 h-4\",\n                                        fill: \"currentColor\",\n                                        viewBox: \"0 0 20 20\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            fillRule: \"evenodd\",\n                                            d: \"M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z\",\n                                            clipRule: \"evenodd\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                            lineNumber: 820,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                        lineNumber: 819,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-medium\",\n                                        children: \"此操作不可恢复！\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                        lineNumber: 822,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                lineNumber: 818,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"mt-1 text-sm text-red-500\",\n                                children: \"删除班级将永久移除班级信息，包括班级设置、模板配置等数据。\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                lineNumber: 824,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                        lineNumber: 817,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                lineNumber: 815,\n                columnNumber: 9\n            }, undefined),\n            okText: \"确定删除\",\n            cancelText: \"取消\",\n            okButtonProps: {\n                danger: true\n            },\n            centered: true,\n            onOk: async ()=>{\n                try {\n                    const response = await _lib_api_class__WEBPACK_IMPORTED_MODULE_2__.classApi.deleteClass(classInfo.id);\n                    if (response.data.code === 200) {\n                        notification.success(\"删除班级成功\");\n                        // 通知父组件班级已被删除\n                        onClassDeleted === null || onClassDeleted === void 0 ? void 0 : onClassDeleted(classInfo.id);\n                        // 返回到班级管理页面\n                        onBack();\n                    } else {\n                        notification.error(response.data.message || \"删除班级失败\");\n                    }\n                } catch (error) {\n                    var _error_response_data, _error_response;\n                    console.error(\"删除班级失败:\", error);\n                    if ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) {\n                        notification.error(error.response.data.message);\n                    } else {\n                        notification.error(\"删除班级失败，请稍后重试\");\n                    }\n                }\n            }\n        });\n    };\n    // 下拉菜单项处理函数\n    const handleMenuItemClick = (action)=>{\n        // setIsMoreActionsDropdownOpen(false); // 已移除\n        switch(action){\n            case \"edit\":\n                setIsEditClassModalVisible(true);\n                break;\n            case \"addStudent\":\n                setIsAddStudentModalVisible(true);\n                break;\n            case \"importStudent\":\n                setIsImportStudentModalVisible(true);\n                break;\n            case \"exportStudent\":\n                handleExportStudents();\n                break;\n            case \"transfer\":\n                setIsTransferClassModalVisible(true);\n                break;\n            case \"invite\":\n                handleGenerateInviteCode();\n                break;\n            case \"batchRedeem\":\n                console.log(\"批量兑换密令\");\n                setIsBatchUseKeyPackageModalVisible(true);\n                break;\n            case \"assignBlocks\":\n                handleAssignBlocks();\n                break;\n            case \"deleteClass\":\n                handleDeleteClass();\n                break;\n            default:\n                break;\n        }\n    };\n    // 设置下拉菜单项处理函数\n    const handleSettingsMenuItemClick = (action)=>{\n        setIsSettingsDropdownOpen(false);\n        switch(action){\n            case \"edit\":\n                setIsEditClassModalVisible(true);\n                break;\n            case \"addStudent\":\n                setIsAddStudentModalVisible(true);\n                break;\n            case \"importStudent\":\n                setIsImportStudentModalVisible(true);\n                break;\n            case \"exportStudent\":\n                handleExportStudents();\n                break;\n            case \"transfer\":\n                setIsTransferClassModalVisible(true);\n                break;\n            case \"invite\":\n                handleGenerateInviteCode();\n                break;\n            case \"batchRedeem\":\n                console.log(\"批量兑换密令\");\n                setIsBatchUseKeyPackageModalVisible(true);\n                break;\n            case \"assignBlocks\":\n                handleAssignBlocks();\n                break;\n            case \"deleteClass\":\n                handleDeleteClass();\n                break;\n            default:\n                break;\n        }\n    };\n    // 确保选中状态同步\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const shouldBeSelectAll = selectedStudentIds.length > 0 && selectedStudentIds.length === students.length;\n        if (isSelectAll !== shouldBeSelectAll) {\n            console.log(\"修复选中状态同步:\", {\n                isSelectAll,\n                shouldBeSelectAll,\n                selectedStudentIds: selectedStudentIds.length,\n                totalStudents: students.length\n            });\n            setIsSelectAll(shouldBeSelectAll);\n        }\n    }, [\n        selectedStudentIds,\n        students.length,\n        isSelectAll\n    ]);\n    // 全选/取消全选处理函数\n    const handleSelectAll = ()=>{\n        if (isSelectAll) {\n            // 取消全选\n            setSelectedStudentIds([]);\n            setIsSelectAll(false);\n        } else {\n            // 全选\n            const allStudentIds = students.map((student)=>student.userId);\n            setSelectedStudentIds(allStudentIds);\n            setIsSelectAll(true);\n        }\n    };\n    // 单个学生选择处理函数\n    const handleStudentSelect = (studentId)=>{\n        if (selectedStudentIds.includes(studentId)) {\n            // 取消选择\n            const newSelectedIds = selectedStudentIds.filter((id)=>id !== studentId);\n            setSelectedStudentIds(newSelectedIds);\n            setIsSelectAll(false);\n        } else {\n            // 选择\n            const newSelectedIds = [\n                ...selectedStudentIds,\n                studentId\n            ];\n            setSelectedStudentIds(newSelectedIds);\n            // 检查是否全选\n            if (newSelectedIds.length === students.length) {\n                setIsSelectAll(true);\n            }\n        }\n    };\n    // 批量操作处理函数\n    const handleBatchAction = (action)=>{\n        setIsBatchActionsDropdownOpen(false);\n        if (selectedStudentIds.length === 0) {\n            Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! logic-common/dist/components/Notification */ \"(app-pages-browser)/./node_modules/logic-common/dist/components/Notification/index.js\")).then((param)=>{\n                let { GetNotification } = param;\n                GetNotification().warning(\"请先选择要操作的学生\");\n            });\n            return;\n        }\n        switch(action){\n            case \"batchDelete\":\n                handleBatchRemoveStudents(selectedStudentIds);\n                break;\n            case \"batchAssignBlocks\":\n                handleAssignBlocks();\n                break;\n            case \"batchAssignPoints\":\n                // 清除单个学生选择，确保进入批量模式\n                setSelectedStudent(null);\n                setIsAssignPointsModalVisible(true);\n                break;\n            case \"batchUseKeyPackage\":\n                handleBatchUseKeyPackage();\n                break;\n            case \"batchExport\":\n                console.log(\"批量导出学生:\", selectedStudentIds);\n                Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! logic-common/dist/components/Notification */ \"(app-pages-browser)/./node_modules/logic-common/dist/components/Notification/index.js\")).then((param)=>{\n                    let { GetNotification } = param;\n                    GetNotification().info(\"批量导出学生功能正在开发中\");\n                });\n                break;\n            default:\n                break;\n        }\n    };\n    // 批量移出班级的改进版本\n    const handleBatchRemoveStudents = async (studentIds)=>{\n        try {\n            // 获取选中的学生信息\n            const selectedStudentsInfo = students.filter((s)=>studentIds.includes(s.userId));\n            // 计算总可用积分\n            const totalAvailablePoints = selectedStudentsInfo.reduce((sum, student)=>sum + (student.availablePoints || 0), 0);\n            const { Modal } = await __webpack_require__.e(/*! import() */ \"_app-pages-browser_node_modules_antd_es_index_js\").then(__webpack_require__.bind(__webpack_require__, /*! antd */ \"(app-pages-browser)/./node_modules/antd/es/index.js\"));\n            const { InfoCircleOutlined } = await __webpack_require__.e(/*! import() */ \"_app-pages-browser_node_modules_ant-design_icons_es_index_js\").then(__webpack_require__.bind(__webpack_require__, /*! @ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/index.js\"));\n            Modal.confirm({\n                title: \"确认批量移出班级\",\n                content: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            children: [\n                                \"确定要将选中的 \",\n                                studentIds.length,\n                                \" 名学生移出班级吗？\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                            lineNumber: 1037,\n                            columnNumber: 13\n                        }, undefined),\n                        totalAvailablePoints > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-2 p-3 bg-yellow-50 rounded-lg\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2 text-yellow-600\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(InfoCircleOutlined, {}, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                            lineNumber: 1041,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: [\n                                                \"选中的学生共有 \",\n                                                totalAvailablePoints,\n                                                \" 点可用能量\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                            lineNumber: 1042,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                    lineNumber: 1040,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"mt-1 text-sm text-yellow-500\",\n                                    children: \"移出班级后，可用能量将返还到各自的套餐积分中\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                    lineNumber: 1044,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                            lineNumber: 1039,\n                            columnNumber: 15\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                    lineNumber: 1036,\n                    columnNumber: 11\n                }, undefined),\n                okText: \"确定移出\",\n                cancelText: \"取消\",\n                centered: true,\n                okButtonProps: {\n                    danger: true\n                },\n                onOk: async ()=>{\n                    try {\n                        const { GetNotification } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! logic-common/dist/components/Notification */ \"(app-pages-browser)/./node_modules/logic-common/dist/components/Notification/index.js\"));\n                        const notification = GetNotification();\n                        const hideLoading = notification.loading(\"正在移出学生...\");\n                        const response = await _lib_api_class__WEBPACK_IMPORTED_MODULE_2__.classApi.removeStudentFromClass(studentIds);\n                        if (hideLoading) {\n                            hideLoading.close();\n                        }\n                        if (response.data.code === 200) {\n                            notification.success(\"成功移出 \".concat(studentIds.length, \" 名学生\"));\n                            // 清除选择状态\n                            setSelectedStudentIds([]);\n                            setIsSelectAll(false);\n                            setSelectedStudent(null);\n                            // 重新获取学生列表\n                            await fetchStudents();\n                        } else {\n                            notification.error(response.data.message || \"批量移出学生失败\");\n                        }\n                    } catch (error) {\n                        var _error_response_data, _error_response;\n                        const { GetNotification } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! logic-common/dist/components/Notification */ \"(app-pages-browser)/./node_modules/logic-common/dist/components/Notification/index.js\"));\n                        const notification = GetNotification();\n                        notification.error(((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || \"批量移出学生失败\");\n                        console.error(\"批量移出学生失败:\", error);\n                    }\n                }\n            });\n        } catch (error) {\n            console.error(\"批量移出学生失败:\", error);\n        }\n    };\n    // 处理添加学生\n    const handleAddStudent = async (values)=>{\n        try {\n            console.log(\"添加学生:\", values);\n            // 添加默认密码\n            const studentData = {\n                ...values,\n                password: \"123456\"\n            };\n            // 调用添加学生的API\n            const response = await _lib_api_class__WEBPACK_IMPORTED_MODULE_2__.classApi.addStudentToClass(classInfo.id, studentData);\n            console.log(\"添加学生 API 响应:\", response.data);\n            // 检查响应状态\n            if (response.data.code === 200) {\n                console.log(\"添加学生成功\");\n                setIsAddStudentModalVisible(false);\n                // 重新获取学生列表\n                await fetchStudents();\n            } else {\n                console.error(\"添加学生失败:\", response.data.message);\n                alert(response.data.message || \"添加学生失败\");\n            }\n        } catch (error) {\n            console.error(\"添加学生失败:\", error);\n            alert(\"添加学生失败，请稍后重试\");\n        }\n    };\n    // 处理文件上传\n    const handleUpload = async (file)=>{\n        setUploading(true);\n        try {\n            // 这里可以添加文件上传逻辑\n            console.log(\"上传文件:\", file);\n            return {\n                success: true\n            };\n        } catch (error) {\n            console.error(\"文件上传失败:\", error);\n            return {\n                success: false\n            };\n        } finally{\n            setUploading(false);\n        }\n    };\n    // 处理文件删除\n    const handleRemoveFile = async (file)=>{\n        try {\n            setFileList((prev)=>prev.filter((f)=>f.uid !== file.uid));\n            return true;\n        } catch (error) {\n            console.error(\"删除文件失败:\", error);\n            return false;\n        }\n    };\n    // 处理发布任务\n    const handlePublishTask = async (values)=>{\n        try {\n            console.log(\"发布任务:\", values);\n            // 导入taskApi\n            const { default: taskApi } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! ../../../lib/api/task */ \"(app-pages-browser)/./lib/api/task.ts\"));\n            const { GetNotification } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! logic-common/dist/components/Notification */ \"(app-pages-browser)/./node_modules/logic-common/dist/components/Notification/index.js\"));\n            const notification = GetNotification();\n            // 构建任务参数\n            const params = {\n                taskName: values.taskName,\n                taskDescription: values.taskDescription || \"\",\n                taskType: 1,\n                startDate: values.startDate ? new Date(values.startDate) : undefined,\n                endDate: values.endDate ? new Date(values.endDate) : new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),\n                taskContent: values.taskContent || \"\",\n                attachments: fileList.map((file)=>{\n                    var _file_response;\n                    return file.url || ((_file_response = file.response) === null || _file_response === void 0 ? void 0 : _file_response.url);\n                }).filter(Boolean),\n                teacherId: userId,\n                classId: classInfo === null || classInfo === void 0 ? void 0 : classInfo.id,\n                studentIds: selectedStudentIds.length > 0 ? selectedStudentIds : students.map((s)=>s.userId),\n                selfAssessmentItems: values.selfAssessmentItems || [],\n                priority: 1,\n                isPublic: 0,\n                allowLateSubmission: values.allowLateSubmission || false\n            };\n            console.log(\"发布任务参数:\", params);\n            // 调用发布任务API\n            const response = await taskApi.publishTask(params);\n            if (response.data.code === 200) {\n                // 显示详细的成功提示\n                const { Modal } = await __webpack_require__.e(/*! import() */ \"_app-pages-browser_node_modules_antd_es_index_js\").then(__webpack_require__.bind(__webpack_require__, /*! antd */ \"(app-pages-browser)/./node_modules/antd/es/index.js\"));\n                const studentCount = selectedStudentIds.length > 0 ? selectedStudentIds.length : students.length;\n                const className = (classInfo === null || classInfo === void 0 ? void 0 : classInfo.className) || \"当前班级\";\n                Modal.success({\n                    title: \"任务发布成功\",\n                    content: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: [\n                                    \"任务 \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: params.taskName\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                        lineNumber: 1194,\n                                        columnNumber: 21\n                                    }, undefined),\n                                    \" 已成功发布到 \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: className\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                        lineNumber: 1194,\n                                        columnNumber: 63\n                                    }, undefined),\n                                    \"。\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                lineNumber: 1194,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: [\n                                    \"共有 \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: studentCount\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                        lineNumber: 1195,\n                                        columnNumber: 21\n                                    }, undefined),\n                                    \" 名学生将收到此任务。\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                lineNumber: 1195,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"学生可以在班级空间查看和提交任务。\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                lineNumber: 1196,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                        lineNumber: 1193,\n                        columnNumber: 13\n                    }, undefined),\n                    okText: \"确定\",\n                    onOk: ()=>{\n                    // 可以在这里添加导航到任务管理页面的逻辑\n                    }\n                });\n                setIsPublishTaskModalVisible(false);\n                // 清理表单数据\n                setFileList([]);\n                setSelectedStudentIds([]);\n                setSelectedTemplateId(null);\n                // 刷新学生列表（如果需要显示任务相关信息）\n                await fetchStudents();\n            } else {\n                notification.error(response.data.message || \"任务发布失败\");\n            }\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error(\"发布任务失败:\", error);\n            const { GetNotification } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! logic-common/dist/components/Notification */ \"(app-pages-browser)/./node_modules/logic-common/dist/components/Notification/index.js\"));\n            const notification = GetNotification();\n            notification.error(((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || \"发布任务失败，请稍后重试\");\n        }\n    };\n    // 处理重置密码\n    const handleResetPassword = async ()=>{\n        try {\n            if (!selectedStudent) {\n                alert(\"请先选择要重置密码的学生\");\n                return;\n            }\n            console.log(\"重置密码:\", selectedStudent);\n            // 这里可以添加重置密码的API调用\n            setIsResetPasswordModalVisible(false);\n            alert(\"密码重置成功，新密码为：123456\");\n        } catch (error) {\n            console.error(\"重置密码失败:\", error);\n            alert(\"重置密码失败，请稍后重试\");\n        }\n    };\n    // 处理选择模板\n    const handleSelectTemplate = async (templateId)=>{\n        try {\n            const { addUserJoinRole, batchAddUserJoinRole } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! ../../../lib/api/role */ \"(app-pages-browser)/./lib/api/role.ts\"));\n            const { GetNotification } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! logic-common/dist/components/Notification */ \"(app-pages-browser)/./node_modules/logic-common/dist/components/Notification/index.js\"));\n            const notification = GetNotification();\n            // 确定要分配的学生\n            const targetStudents = selectedStudentId !== null ? [\n                selectedStudentId\n            ] : selectedStudentIds;\n            console.log(\"=== 模板分配详情 ===\");\n            console.log(\"selectedStudentId:\", selectedStudentId);\n            console.log(\"selectedStudentIds:\", selectedStudentIds);\n            console.log(\"targetStudents:\", targetStudents);\n            console.log(\"templateId:\", templateId);\n            if (targetStudents.length === 0) {\n                console.log(\"❌ 没有选中任何学生\");\n                notification.warning(\"请先选择学生\");\n                return;\n            }\n            const hideLoading = notification.loading(\"正在分配积木...\");\n            const userRolesMap = userRoles || [];\n            try {\n                // 准备用户数据 - 与teacher-space保持一致的逻辑\n                const usersData = targetStudents.map((userId)=>{\n                    const userInfo = userRolesMap.find((u)=>u.userId === userId);\n                    if (!(userInfo === null || userInfo === void 0 ? void 0 : userInfo.roleId)) return null;\n                    return {\n                        userId: userId,\n                        roleId: userInfo.roleId,\n                        templateId: templateId,\n                        originalTemplateId: templateId\n                    };\n                }).filter((item)=>item !== null);\n                console.log(\"准备分配模板:\", {\n                    templateId,\n                    targetStudents,\n                    userRolesMap,\n                    usersData\n                });\n                if (usersData.length === 0) {\n                    notification.error(\"无有效用户可分配\");\n                    return;\n                }\n                // 分批并发处理\n                const results = await (usersData.length > 20 ? Promise.all(Array.from({\n                    length: Math.ceil(usersData.length / 20)\n                }, (_, i)=>usersData.slice(i * 20, (i + 1) * 20)).map((batchUsers)=>batchAddUserJoinRole({\n                        users: batchUsers\n                    }).then((param)=>{\n                        let { data } = param;\n                        return data;\n                    }).catch(()=>({\n                            code: 500,\n                            data: {\n                                successCount: 0,\n                                failCount: batchUsers.length\n                            }\n                        })))).then((results)=>({\n                        code: results.some((r)=>r.code !== 200) ? 500 : 200,\n                        data: {\n                            successCount: results.reduce((sum, r)=>{\n                                var _r_data;\n                                return sum + (((_r_data = r.data) === null || _r_data === void 0 ? void 0 : _r_data.successCount) || 0);\n                            }, 0),\n                            failCount: results.reduce((sum, r)=>{\n                                var _r_data;\n                                return sum + (((_r_data = r.data) === null || _r_data === void 0 ? void 0 : _r_data.failCount) || 0);\n                            }, 0)\n                        }\n                    })) : batchAddUserJoinRole({\n                    users: usersData\n                }).then((param)=>{\n                    let { data } = param;\n                    return data;\n                }).catch(()=>({\n                        code: 500,\n                        data: {\n                            successCount: 0,\n                            failCount: usersData.length\n                        }\n                    })));\n                if (hideLoading) {\n                    hideLoading.close();\n                }\n                // 显示结果\n                if (results.code === 200) {\n                    const { successCount = 0, failCount = 0 } = results.data || {};\n                    if (successCount > 0 && failCount === 0) {\n                        notification.success(\"成功为 \".concat(successCount, \" 名学生分配积木\"));\n                    } else if (successCount > 0 && failCount > 0) {\n                        notification.warning(\"成功为 \".concat(successCount, \" 名学生分配积木，\").concat(failCount, \" 名学生分配失败\"));\n                    } else {\n                        notification.error(\"积木分配失败\");\n                    }\n                } else {\n                    notification.error(\"积木分配失败\");\n                }\n                // 立即更新已分配学生的模板信息，无需等待API刷新\n                const selectedTemplate = templates.find((t)=>t.id === templateId);\n                if (selectedTemplate) {\n                    const templateData = {\n                        templateId: templateId,\n                        templateName: selectedTemplate.templateName || selectedTemplate.name,\n                        isOfficial: selectedTemplate.isOfficial || false\n                    };\n                    // 更新personalTemplateAssignments Map，确保数据持久化\n                    setPersonalTemplateAssignments((prev)=>{\n                        const newMap = new Map(prev);\n                        targetStudents.forEach((studentId)=>{\n                            newMap.set(studentId, templateData);\n                        });\n                        return newMap;\n                    });\n                    // 更新学生状态\n                    const updatedStudents = students.map((student)=>{\n                        if (targetStudents.includes(student.userId)) {\n                            // 被选中的学生：设置为新分配的模板\n                            return {\n                                ...student,\n                                currentTemplate: templateData\n                            };\n                        }\n                        // 未选中的学生：保持原有状态\n                        return student;\n                    });\n                    setStudents(updatedStudents);\n                    console.log(\"模板分配成功，已更新personalTemplateAssignments:\", {\n                        targetStudents,\n                        templateData,\n                        personalTemplateAssignmentsBefore: Array.from(personalTemplateAssignments.entries()),\n                        personalTemplateAssignmentsAfter: \"will be updated\"\n                    });\n                    // 延迟打印更新后的状态\n                    setTimeout(()=>{\n                        console.log(\"personalTemplateAssignments更新后:\", Array.from(personalTemplateAssignments.entries()));\n                    }, 100);\n                }\n                // 关闭弹窗并清理状态\n                setIsAssignBlocksModalVisible(false);\n                setSelectedStudentId(null);\n                setSelectedStudentIds([]); // 清空选中的学生\n                setIsSelectAll(false); // 取消全选状态\n                // 刷新相关数据\n                await fetchTemplateUsage(); // 刷新模板使用情况\n            // 当前模板信息由全局状态管理，无需手动刷新\n            } catch (error) {\n                if (hideLoading) {\n                    hideLoading.close();\n                }\n                throw error;\n            }\n        } catch (error) {\n            console.error(\"分配模板失败:\", error);\n            const { GetNotification } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! logic-common/dist/components/Notification */ \"(app-pages-browser)/./node_modules/logic-common/dist/components/Notification/index.js\"));\n            const notification = GetNotification();\n            notification.error(\"分配模板失败，请稍后重试\");\n        }\n    };\n    // 处理模板使用情况点击\n    const handleTemplateUsageClick = (e, template)=>{\n        e.stopPropagation();\n        console.log(\"查看模板使用情况:\", template);\n    };\n    // 处理单个学生分配能量\n    const handleAssignPoints = async (values)=>{\n        var _values_studentExpiries;\n        if (!selectedStudent) {\n            const { GetNotification } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! logic-common/dist/components/Notification */ \"(app-pages-browser)/./node_modules/logic-common/dist/components/Notification/index.js\"));\n            const notification = GetNotification();\n            notification.error(\"请先选择学生\");\n            return;\n        }\n        // 从 studentExpiries 中提取单个学生的过期时间\n        const expireTime = (_values_studentExpiries = values.studentExpiries) === null || _values_studentExpiries === void 0 ? void 0 : _values_studentExpiries[selectedStudent.userId];\n        try {\n            const { GetNotification } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! logic-common/dist/components/Notification */ \"(app-pages-browser)/./node_modules/logic-common/dist/components/Notification/index.js\"));\n            const notification = GetNotification();\n            const hideLoading = notification.loading(\"正在分配能量...\");\n            const { pointsApi } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! ../../../lib/api/points */ \"(app-pages-browser)/./lib/api/points.ts\"));\n            await pointsApi.assignPermission({\n                studentUserId: selectedStudent.userId,\n                availablePoints: values.availablePoints,\n                expireTime: expireTime,\n                remark: values.remark\n            });\n            if (hideLoading) {\n                hideLoading.close();\n            }\n            notification.success(\"分配能量成功\");\n            setIsAssignPointsModalVisible(false);\n            // 刷新学生列表\n            await refreshStudentList();\n        } catch (error) {\n            var _error_response_data, _error_response;\n            const { GetNotification } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! logic-common/dist/components/Notification */ \"(app-pages-browser)/./node_modules/logic-common/dist/components/Notification/index.js\"));\n            const notification = GetNotification();\n            // 增加更具体的错误提示\n            notification.error(((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || \"分配能量失败\");\n        }\n    };\n    // 刷新学生列表\n    const refreshStudentList = async ()=>{\n        await fetchStudents();\n    };\n    // 更新教师的当前模板（通过UserJoinRole表）\n    const updateClassCurrentTemplate = async (templateId, templateName, isOfficial)=>{\n        try {\n            console.log(\"更新教师当前模板:\", {\n                userId,\n                roleId,\n                templateId,\n                templateName,\n                isOfficial\n            });\n            // 使用addUserJoinRole API来更新教师的模板\n            const { addUserJoinRole } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! ../../../lib/api/role */ \"(app-pages-browser)/./lib/api/role.ts\"));\n            const response = await addUserJoinRole({\n                userId: userId,\n                roleId: roleId || 2,\n                templateId: templateId\n            });\n            if (response.data.code === 200) {\n                // 更新本地的全局当前模板状态\n                const newTemplate = {\n                    templateId: templateId,\n                    templateName: templateName,\n                    isOfficial: isOfficial\n                };\n                setGlobalCurrentTemplate(newTemplate);\n                console.log(\"教师当前模板更新成功:\", newTemplate);\n            } else {\n                console.error(\"更新教师当前模板失败:\", response.data);\n            }\n        } catch (error) {\n            console.error(\"更新教师当前模板失败:\", error);\n        }\n    };\n    // 更新学生的当前模板信息\n    const handleUpdateStudentTemplate = (studentIds, templateInfo)=>{\n        const templateData = {\n            templateId: templateInfo.templateId,\n            templateName: templateInfo.templateName,\n            isOfficial: templateInfo.isOfficial || false\n        };\n        // 保存个人分配的模板信息\n        setPersonalTemplateAssignments((prev)=>{\n            const newMap = new Map(prev);\n            studentIds.forEach((studentId)=>{\n                newMap.set(studentId, templateData);\n            });\n            return newMap;\n        });\n        // 更新学生状态\n        setStudents((prevStudents)=>prevStudents.map((student)=>{\n                if (studentIds.includes(student.userId)) {\n                    return {\n                        ...student,\n                        currentTemplate: templateData\n                    };\n                }\n                return student;\n            }));\n    // 不再自动更新教师模板，只分配给学生\n    // updateClassCurrentTemplate(templateData.templateId, templateData.templateName, templateData.isOfficial);\n    };\n    // 处理批量/单个兑换密钥\n    const handleBatchUseKeyPackage = (studentId)=>{\n        if (studentId) {\n            // 单个学生触发\n            setSelectedStudentIds([\n                studentId\n            ]);\n            setIsBatchUseKeyPackageModalVisible(true);\n        } else {\n            // 批量操作触发\n            if (selectedStudentIds.length === 0) {\n                Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! logic-common/dist/components/Notification */ \"(app-pages-browser)/./node_modules/logic-common/dist/components/Notification/index.js\")).then((param)=>{\n                    let { GetNotification } = param;\n                    GetNotification().warning(\"请先选择学生\");\n                });\n                return;\n            }\n            setIsBatchUseKeyPackageModalVisible(true);\n        }\n    };\n    // 处理兑换密令成功\n    const handleBatchUseKeyPackageSuccess = ()=>{\n        refreshStudentList();\n        setIsBatchUseKeyPackageModalVisible(false);\n    };\n    // 处理从兑换密令跳转到分配能量\n    const handleGoToAssignPointsFromRedeem = (studentIds)=>{\n        setIsBatchUseKeyPackageModalVisible(false);\n        // 设置选中的学生\n        setSelectedStudentIds(studentIds);\n        // 打开分配能量弹窗\n        setIsAssignPointsModalVisible(true);\n    };\n    // 批量分配能量处理函数\n    const handleBatchAssignPoints = async (values)=>{\n        if (selectedStudentIds.length === 0) {\n            const { GetNotification } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! logic-common/dist/components/Notification */ \"(app-pages-browser)/./node_modules/logic-common/dist/components/Notification/index.js\"));\n            const notification = GetNotification();\n            notification.error(\"请先选择学生\");\n            return;\n        }\n        if (!values.studentExpiries) {\n            const { GetNotification } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! logic-common/dist/components/Notification */ \"(app-pages-browser)/./node_modules/logic-common/dist/components/Notification/index.js\"));\n            const notification = GetNotification();\n            notification.error(\"未能获取学生过期时间信息\");\n            return;\n        }\n        try {\n            const { GetNotification } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! logic-common/dist/components/Notification */ \"(app-pages-browser)/./node_modules/logic-common/dist/components/Notification/index.js\"));\n            const notification = GetNotification();\n            const hideLoading = notification.loading(\"正在批量分配能量...\");\n            // 调用新的批量分配 API\n            const { pointsApi } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! ../../../lib/api/points */ \"(app-pages-browser)/./lib/api/points.ts\"));\n            const response = await pointsApi.batchAssignPermission({\n                availablePoints: values.availablePoints,\n                studentExpiries: values.studentExpiries,\n                remark: values.remark\n            });\n            console.log(\"批量分配积分res\", response);\n            if (hideLoading) {\n                hideLoading.close();\n            }\n            if (response.data.code === 200) {\n                // 后端现在返回处理结果数组，可以根据需要处理\n                const results = response.data.data;\n                // 可以根据 results 中的信息给出更详细的成功/失败提示，\n                // 但为了简单起见，我们仍然使用之前的逻辑\n                notification.success(\"成功为 \".concat(results.success, \" 名学生分配能量\"));\n                setIsAssignPointsModalVisible(false);\n                // 刷新学生列表\n                await refreshStudentList(); // 确保使用 await\n                setSelectedStudentIds([]); // 清空选择\n                setIsSelectAll(false);\n            } else {\n                // 处理 API 返回的错误信息\n                notification.error(response.data.message || \"批量分配能量失败\");\n            }\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error(\"批量分配能量失败:\", error);\n            const { GetNotification } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! logic-common/dist/components/Notification */ \"(app-pages-browser)/./node_modules/logic-common/dist/components/Notification/index.js\"));\n            const notification = GetNotification();\n            // 处理请求级别的错误\n            notification.error(((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || \"批量分配能量失败，请检查网络连接或稍后重试\");\n        }\n    };\n    if (!selectedSchool) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"class-detail-container\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"class-detail-content\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"error-message\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            children: \"学校信息不存在\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                            lineNumber: 1624,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: onBack,\n                            className: \"back-button\",\n                            children: \"返回班级管理\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                            lineNumber: 1625,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                    lineNumber: 1623,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                lineNumber: 1622,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n            lineNumber: 1621,\n            columnNumber: 7\n        }, undefined);\n    }\n    // 生成随机头像颜色\n    const getAvatarColor = (index)=>{\n        const colors = [\n            \"linear-gradient(135deg, #667eea 0%, #764ba2 100%)\",\n            \"linear-gradient(135deg, #f093fb 0%, #f5576c 100%)\",\n            \"linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)\",\n            \"linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)\",\n            \"linear-gradient(135deg, #fa709a 0%, #fee140 100%)\",\n            \"linear-gradient(135deg, #a8edea 0%, #fed6e3 100%)\"\n        ];\n        // 确保index是有效的正数\n        const safeIndex = Math.max(0, index);\n        return colors[safeIndex % colors.length];\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"class-detail-container\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"class-detail-header\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"breadcrumb-nav\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: \"back-button\",\n                                onClick: onBack,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Blocks_ChevronDown_Download_Edit_Gift_Link_Plus_Search_Settings_Trash2_Upload_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        size: 18\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                        lineNumber: 1656,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"班级管理\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                        lineNumber: 1657,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                lineNumber: 1655,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"breadcrumb-separator\",\n                                children: \"/\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                lineNumber: 1659,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"current-page\",\n                                children: classInfo.className\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                lineNumber: 1660,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                        lineNumber: 1654,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"class-info-card\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"class-avatar\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"robot-avatar\",\n                                    children: \"\\uD83E\\uDD16\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                    lineNumber: 1666,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                lineNumber: 1665,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"class-details\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"class-name\",\n                                        children: classInfo.className\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                        lineNumber: 1669,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"school-name\",\n                                        children: selectedSchool.schoolName\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                        lineNumber: 1670,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                lineNumber: 1668,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"class-stats\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"stat-item\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"stat-number\",\n                                            children: students.length\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                            lineNumber: 1674,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"stat-label\",\n                                            children: \"学生\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                            lineNumber: 1675,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                    lineNumber: 1673,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                lineNumber: 1672,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"settings-container\",\n                                ref: settingsDropdownRef,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"settings-btn \".concat(isSettingsDropdownOpen ? \"active\" : \"\"),\n                                        onClick: ()=>setIsSettingsDropdownOpen(!isSettingsDropdownOpen),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Blocks_ChevronDown_Download_Edit_Gift_Link_Plus_Search_Settings_Trash2_Upload_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                            size: 20\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                            lineNumber: 1685,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                        lineNumber: 1681,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    isSettingsDropdownOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"dropdown-menu\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"dropdown-menu-items\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"dropdown-menu-item\",\n                                                    onClick: ()=>handleSettingsMenuItemClick(\"edit\"),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"dropdown-menu-item-icon\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Blocks_ChevronDown_Download_Edit_Gift_Link_Plus_Search_Settings_Trash2_Upload_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                size: 16,\n                                                                style: {\n                                                                    color: \"#8b5cf6\"\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                lineNumber: 1696,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                            lineNumber: 1695,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"编辑班级\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                            lineNumber: 1698,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                    lineNumber: 1691,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"dropdown-menu-item\",\n                                                    onClick: ()=>handleSettingsMenuItemClick(\"addStudent\"),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"dropdown-menu-item-icon\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Blocks_ChevronDown_Download_Edit_Gift_Link_Plus_Search_Settings_Trash2_Upload_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                size: 16,\n                                                                style: {\n                                                                    color: \"#10b981\"\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                lineNumber: 1706,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                            lineNumber: 1705,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"添加学生\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                            lineNumber: 1708,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                    lineNumber: 1701,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"dropdown-menu-item\",\n                                                    onClick: ()=>handleSettingsMenuItemClick(\"importStudent\"),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"dropdown-menu-item-icon\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Blocks_ChevronDown_Download_Edit_Gift_Link_Plus_Search_Settings_Trash2_Upload_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                size: 16,\n                                                                style: {\n                                                                    color: \"#3b82f6\"\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                lineNumber: 1716,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                            lineNumber: 1715,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"导入学生\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                            lineNumber: 1718,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                    lineNumber: 1711,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"dropdown-menu-item\",\n                                                    onClick: ()=>handleSettingsMenuItemClick(\"exportStudent\"),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"dropdown-menu-item-icon\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Blocks_ChevronDown_Download_Edit_Gift_Link_Plus_Search_Settings_Trash2_Upload_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                size: 16,\n                                                                style: {\n                                                                    color: \"#10b981\"\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                lineNumber: 1726,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                            lineNumber: 1725,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"导出学生\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                            lineNumber: 1728,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                    lineNumber: 1721,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"dropdown-menu-item\",\n                                                    onClick: ()=>handleSettingsMenuItemClick(\"transfer\"),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"dropdown-menu-item-icon\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Blocks_ChevronDown_Download_Edit_Gift_Link_Plus_Search_Settings_Trash2_Upload_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                size: 16,\n                                                                style: {\n                                                                    color: \"#f59e0b\"\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                lineNumber: 1736,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                            lineNumber: 1735,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"转让管理\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                            lineNumber: 1738,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                    lineNumber: 1731,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"dropdown-menu-item\",\n                                                    onClick: ()=>handleSettingsMenuItemClick(\"invite\"),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"dropdown-menu-item-icon\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Blocks_ChevronDown_Download_Edit_Gift_Link_Plus_Search_Settings_Trash2_Upload_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                size: 16,\n                                                                style: {\n                                                                    color: \"#3b82f6\"\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                lineNumber: 1746,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                            lineNumber: 1745,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"生成邀请码\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                            lineNumber: 1748,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                    lineNumber: 1741,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"dropdown-menu-item\",\n                                                    onClick: ()=>handleSettingsMenuItemClick(\"batchRedeem\"),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"dropdown-menu-item-icon\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Blocks_ChevronDown_Download_Edit_Gift_Link_Plus_Search_Settings_Trash2_Upload_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                size: 16,\n                                                                style: {\n                                                                    color: \"#f59e0b\"\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                lineNumber: 1756,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                            lineNumber: 1755,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"批量兑换密令\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                            lineNumber: 1758,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                    lineNumber: 1751,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"dropdown-menu-item\",\n                                                    onClick: ()=>handleSettingsMenuItemClick(\"assignBlocks\"),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"dropdown-menu-item-icon\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Blocks_ChevronDown_Download_Edit_Gift_Link_Plus_Search_Settings_Trash2_Upload_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                size: 16,\n                                                                style: {\n                                                                    color: \"#3b82f6\"\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                lineNumber: 1766,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                            lineNumber: 1765,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"分配积木\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                            lineNumber: 1768,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                    lineNumber: 1761,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"dropdown-menu-divider\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                    lineNumber: 1771,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"dropdown-menu-item danger\",\n                                                    onClick: ()=>handleSettingsMenuItemClick(\"deleteClass\"),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"dropdown-menu-item-icon\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Blocks_ChevronDown_Download_Edit_Gift_Link_Plus_Search_Settings_Trash2_Upload_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                size: 16\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                lineNumber: 1778,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                            lineNumber: 1777,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"删除班级\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                            lineNumber: 1780,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                    lineNumber: 1773,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                            lineNumber: 1690,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                        lineNumber: 1689,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                lineNumber: 1680,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                        lineNumber: 1664,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                lineNumber: 1652,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"main-content\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"students-management-section\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"section-header\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        children: \"学生管理\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                        lineNumber: 1795,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"header-actions\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"search-box\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Blocks_ChevronDown_Download_Edit_Gift_Link_Plus_Search_Settings_Trash2_Upload_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                        size: 16,\n                                                        className: \"search-icon\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                        lineNumber: 1798,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        placeholder: \"搜索学生...\",\n                                                        className: \"search-input\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                        lineNumber: 1799,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                lineNumber: 1797,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"add-student-btn\",\n                                                onClick: ()=>setIsAddStudentModalVisible(true),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Blocks_ChevronDown_Download_Edit_Gift_Link_Plus_Search_Settings_Trash2_Upload_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                        size: 16\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                        lineNumber: 1809,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"添加学生\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                        lineNumber: 1810,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                lineNumber: 1805,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                        lineNumber: 1796,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                lineNumber: 1794,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"students-toolbar\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"toolbar-left\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"select-all-btn \".concat(isSelectAll ? \"active\" : \"\"),\n                                                onClick: ()=>{\n                                                    console.log(\"点击全选按钮，当前状态:\", {\n                                                        isSelectAll,\n                                                        selectedStudentIds: selectedStudentIds.length,\n                                                        totalStudents: students.length\n                                                    });\n                                                    handleSelectAll();\n                                                },\n                                                title: isSelectAll ? \"取消全选\" : \"全选\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: isSelectAll ? \"取消全选\" : \"全选\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                    lineNumber: 1830,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                lineNumber: 1818,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"student-count\",\n                                                children: [\n                                                    \"共 \",\n                                                    students.length,\n                                                    \" 名学生\",\n                                                    selectedStudentIds.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"selected-count\",\n                                                        children: [\n                                                            \"，已选择 \",\n                                                            selectedStudentIds.length,\n                                                            \" 名\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                        lineNumber: 1836,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                lineNumber: 1833,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                        lineNumber: 1817,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"toolbar-right\",\n                                        children: selectedStudentIds.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"batch-actions-container\",\n                                            ref: batchActionsDropdownRef,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: \"batch-actions-btn\",\n                                                    onClick: ()=>setIsBatchActionsDropdownOpen(!isBatchActionsDropdownOpen),\n                                                    title: \"批量操作\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: [\n                                                                \"批量操作(\",\n                                                                selectedStudentIds.length,\n                                                                \")\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                            lineNumber: 1850,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Blocks_ChevronDown_Download_Edit_Gift_Link_Plus_Search_Settings_Trash2_Upload_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                            size: 16\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                            lineNumber: 1851,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                    lineNumber: 1845,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                isBatchActionsDropdownOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"batch-actions-dropdown\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            className: \"batch-action-item\",\n                                                            onClick: ()=>handleBatchAction(\"batchAssignBlocks\"),\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Blocks_ChevronDown_Download_Edit_Gift_Link_Plus_Search_Settings_Trash2_Upload_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                    size: 16\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                    lineNumber: 1861,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"批量分配积木\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                    lineNumber: 1862,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                            lineNumber: 1857,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            className: \"batch-action-item\",\n                                                            onClick: ()=>handleBatchAction(\"batchAssignPoints\"),\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Blocks_ChevronDown_Download_Edit_Gift_Link_Plus_Search_Settings_Trash2_Upload_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                                                    size: 16\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                    lineNumber: 1868,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"批量分配能量\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                    lineNumber: 1869,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                            lineNumber: 1864,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            className: \"batch-action-item\",\n                                                            onClick: ()=>handleBatchAction(\"batchUseKeyPackage\"),\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Blocks_ChevronDown_Download_Edit_Gift_Link_Plus_Search_Settings_Trash2_Upload_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                    size: 16\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                    lineNumber: 1875,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"批量兑换密令\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                    lineNumber: 1876,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                            lineNumber: 1871,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            className: \"batch-action-item\",\n                                                            onClick: ()=>handleBatchAction(\"batchDelete\"),\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Blocks_ChevronDown_Download_Edit_Gift_Link_Plus_Search_Settings_Trash2_Upload_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                    size: 16\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                    lineNumber: 1882,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"批量移出班级\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                    lineNumber: 1883,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                            lineNumber: 1878,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            className: \"batch-action-item\",\n                                                            onClick: ()=>handleBatchAction(\"batchExport\"),\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Blocks_ChevronDown_Download_Edit_Gift_Link_Plus_Search_Settings_Trash2_Upload_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                    size: 16\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                    lineNumber: 1889,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"批量导出\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                    lineNumber: 1890,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                            lineNumber: 1885,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                    lineNumber: 1856,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                            lineNumber: 1844,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                        lineNumber: 1841,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                lineNumber: 1816,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"students-list\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_StudentList__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                    students: students,\n                                    loading: loading,\n                                    error: error,\n                                    selectedStudent: selectedStudent,\n                                    selectedStudentIds: selectedStudentIds,\n                                    currentTemplate: globalCurrentTemplate || currentTemplate,\n                                    renderVersion: renderVersion,\n                                    onStudentClick: handleStudentClick,\n                                    onStudentSelect: handleStudentSelect,\n                                    onRetry: fetchStudents,\n                                    onIndividualAssignBlocks: handleIndividualAssignBlocks,\n                                    onAssignPoints: (studentId)=>{\n                                        const student = students.find((s)=>s.userId === studentId);\n                                        if (student) {\n                                            setSelectedStudent(student);\n                                            setIsAssignPointsModalVisible(true);\n                                        }\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                    lineNumber: 1901,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                lineNumber: 1900,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                        lineNumber: 1793,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"dashboard-section\",\n                        children: [\n                            selectedStudent && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"selected-student-card\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"student-avatar-large\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"student-avatar-circle\",\n                                            style: {\n                                                background: getAvatarColor(Math.max(0, students.findIndex((s)=>s.userId === selectedStudent.userId)))\n                                            },\n                                            children: ((_selectedStudent_nickName = selectedStudent.nickName) === null || _selectedStudent_nickName === void 0 ? void 0 : _selectedStudent_nickName.charAt(0)) || \"S\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                            lineNumber: 1930,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                        lineNumber: 1929,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"student-info\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"student-name\",\n                                                children: selectedStudent.nickName || \"学生\".concat(selectedStudent.studentNumber || selectedStudent.userId)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                lineNumber: 1940,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"student-id\",\n                                                children: [\n                                                    \"学号：\",\n                                                    selectedStudent.studentNumber || \"无学号\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                lineNumber: 1943,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                        lineNumber: 1939,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"remove-student-btn\",\n                                        onClick: handleDeleteStudent,\n                                        title: \"移出班级\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Blocks_ChevronDown_Download_Edit_Gift_Link_Plus_Search_Settings_Trash2_Upload_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                            size: 16\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                            lineNumber: 1952,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                        lineNumber: 1947,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                lineNumber: 1928,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"stats-cards\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"stat-card energy-card\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"stat-header\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"stat-title\",\n                                                        children: \"能量统计\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                        lineNumber: 1961,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"stat-icon\",\n                                                        children: \"⚡\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                        lineNumber: 1962,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                lineNumber: 1960,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"stat-content\",\n                                                children: selectedStudent ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"energy-display\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"energy-available\",\n                                                                    children: selectedStudent.availablePoints || 0\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                    lineNumber: 1968,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"energy-separator\",\n                                                                    children: \"/\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                    lineNumber: 1969,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"energy-total\",\n                                                                    children: selectedStudent.totalPoints || 0\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                    lineNumber: 1970,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                            lineNumber: 1967,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"energy-bar\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"energy-progress\",\n                                                                style: {\n                                                                    width: selectedStudent.totalPoints ? \"\".concat(Number(selectedStudent.availablePoints || 0) / Number(selectedStudent.totalPoints || 0) * 100, \"%\") : \"0%\"\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                lineNumber: 1973,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                            lineNumber: 1972,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"no-selection\",\n                                                    children: \"请选择学生查看详情\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                    lineNumber: 1984,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                lineNumber: 1964,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                        lineNumber: 1959,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"stat-card template-card\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"stat-header\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"stat-title\",\n                                                        children: \"当前模板\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                        lineNumber: 1991,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"stat-icon\",\n                                                        children: \"\\uD83E\\uDDE9\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                        lineNumber: 1992,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                lineNumber: 1990,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"stat-content\",\n                                                children: selectedStudent ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"template-name\",\n                                                            children: (()=>{\n                                                                const displayTemplate = (selectedStudent === null || selectedStudent === void 0 ? void 0 : selectedStudent.currentTemplate) || globalCurrentTemplate || currentTemplate;\n                                                                return displayTemplate ? displayTemplate.templateName : \"加载中...\";\n                                                            })()\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                            lineNumber: 1997,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"template-badge\",\n                                                            children: (()=>{\n                                                                const displayTemplate = (selectedStudent === null || selectedStudent === void 0 ? void 0 : selectedStudent.currentTemplate) || globalCurrentTemplate || currentTemplate;\n                                                                return displayTemplate && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"badge \".concat(displayTemplate.isOfficial ? \"official\" : \"custom\"),\n                                                                    children: displayTemplate.isOfficial ? \"官方\" : \"自定义\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                    lineNumber: 2007,\n                                                                    columnNumber: 27\n                                                                }, undefined);\n                                                            })()\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                            lineNumber: 2003,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"no-selection\",\n                                                    children: \"请选择学生查看详情\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                    lineNumber: 2015,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                lineNumber: 1994,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                        lineNumber: 1989,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                lineNumber: 1958,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"quick-actions\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        children: \"快捷操作\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                        lineNumber: 2023,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"action-grid\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"action-btn primary\",\n                                                onClick: ()=>setIsPublishTaskModalVisible(true),\n                                                disabled: !selectedStudent,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"action-icon\",\n                                                        children: \"\\uD83D\\uDCDD\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                        lineNumber: 2030,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"发布任务\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                        lineNumber: 2031,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                lineNumber: 2025,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"action-btn secondary\",\n                                                onClick: handleAssignBlocks,\n                                                disabled: !selectedStudent,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"action-icon\",\n                                                        children: \"\\uD83E\\uDDE9\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                        lineNumber: 2038,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"分配积木\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                        lineNumber: 2039,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                lineNumber: 2033,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"action-btn secondary\",\n                                                onClick: ()=>setIsAssignPointsModalVisible(true),\n                                                disabled: !selectedStudent,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"action-icon\",\n                                                        children: \"⚡\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                        lineNumber: 2046,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"分配能量\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                        lineNumber: 2047,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                lineNumber: 2041,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"action-btn secondary\",\n                                                onClick: ()=>setIsBatchUseKeyPackageModalVisible(true),\n                                                disabled: !selectedStudent,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"action-icon\",\n                                                        children: \"\\uD83C\\uDF81\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                        lineNumber: 2054,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"兑换密令\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                        lineNumber: 2055,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                lineNumber: 2049,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"action-btn warning\",\n                                                onClick: ()=>setIsResetPasswordModalVisible(true),\n                                                disabled: !selectedStudent,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"action-icon\",\n                                                        children: \"\\uD83D\\uDD11\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                        lineNumber: 2062,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"重置密码\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                        lineNumber: 2063,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                lineNumber: 2057,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                        lineNumber: 2024,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                lineNumber: 2022,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"energy-section\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"energy-header\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"energy-label\",\n                                                children: \"可用能量/总能量\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                lineNumber: 2071,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"energy-display\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"energy-available-number\",\n                                                        children: (selectedStudent === null || selectedStudent === void 0 ? void 0 : selectedStudent.availablePoints) || 0\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                        lineNumber: 2073,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"energy-total-number\",\n                                                        children: [\n                                                            \"/\",\n                                                            (selectedStudent === null || selectedStudent === void 0 ? void 0 : selectedStudent.totalPoints) || 0\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                        lineNumber: 2076,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                lineNumber: 2072,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                        lineNumber: 2070,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"energy-bar\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"energy-progress\",\n                                            style: {\n                                                width: (selectedStudent === null || selectedStudent === void 0 ? void 0 : selectedStudent.totalPoints) ? \"\".concat(Number(selectedStudent.availablePoints || 0) / Number(selectedStudent.totalPoints || 0) * 100, \"%\") : \"0%\"\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                            lineNumber: 2082,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                        lineNumber: 2081,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                lineNumber: 2069,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"template-section\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"template-header\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"当前模板\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                lineNumber: 2096,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            (()=>{\n                                                const displayTemplate = (selectedStudent === null || selectedStudent === void 0 ? void 0 : selectedStudent.currentTemplate) || globalCurrentTemplate || currentTemplate;\n                                                return displayTemplate && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"template-badge \".concat(displayTemplate.isOfficial ? \"\" : \"custom\"),\n                                                    children: displayTemplate.isOfficial ? \"官方\" : \"自定义\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                    lineNumber: 2100,\n                                                    columnNumber: 19\n                                                }, undefined);\n                                            })()\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                        lineNumber: 2095,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"template-info\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"template-icon\",\n                                                children: \"\\uD83E\\uDDE9\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                lineNumber: 2107,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: !(selectedStudent === null || selectedStudent === void 0 ? void 0 : selectedStudent.currentTemplate) && !globalCurrentTemplate && !currentTemplate ? \"template-loading\" : \"\",\n                                                children: (()=>{\n                                                    if (selectedStudent === null || selectedStudent === void 0 ? void 0 : selectedStudent.currentTemplate) {\n                                                        return selectedStudent.currentTemplate.templateName;\n                                                    } else if (globalCurrentTemplate) {\n                                                        return globalCurrentTemplate.templateName;\n                                                    } else if (currentTemplate) {\n                                                        return currentTemplate.templateName;\n                                                    } else if (selectedStudent) {\n                                                        return \"加载中...\";\n                                                    } else {\n                                                        return \"请选择学生\";\n                                                    }\n                                                })()\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                lineNumber: 2108,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            !(selectedStudent === null || selectedStudent === void 0 ? void 0 : selectedStudent.currentTemplate) && !globalCurrentTemplate && !currentTemplate && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: async ()=>{\n                                                    await fetchTeacherCurrentTemplate();\n                                                    await refreshCurrentTemplate();\n                                                },\n                                                style: {\n                                                    marginLeft: \"8px\",\n                                                    padding: \"2px 6px\",\n                                                    fontSize: \"10px\",\n                                                    backgroundColor: \"#2196F3\",\n                                                    color: \"white\",\n                                                    border: \"none\",\n                                                    borderRadius: \"3px\",\n                                                    cursor: \"pointer\"\n                                                },\n                                                children: \"刷新\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                lineNumber: 2125,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                        lineNumber: 2106,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                lineNumber: 2094,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                        lineNumber: 1925,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                lineNumber: 1791,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AddStudentModal__WEBPACK_IMPORTED_MODULE_5__.AddStudentModal, {\n                visible: isAddStudentModalVisible,\n                onCancel: ()=>setIsAddStudentModalVisible(false),\n                onOk: handleAddStudent\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                lineNumber: 2150,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_teacher_space_components_modals__WEBPACK_IMPORTED_MODULE_6__.EditClassModal, {\n                visible: isEditClassModalVisible,\n                onCancel: ()=>setIsEditClassModalVisible(false),\n                onOk: handleEditClass,\n                initialValues: {\n                    className: classInfo.className\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                lineNumber: 2157,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_teacher_space_components_modals__WEBPACK_IMPORTED_MODULE_6__.ImportStudentModal, {\n                visible: isImportStudentModalVisible,\n                onCancel: ()=>setIsImportStudentModalVisible(false),\n                onImport: handleImportStudents,\n                classId: classInfo.id\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                lineNumber: 2167,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_TransferClassModal__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                visible: isTransferClassModalVisible,\n                onCancel: ()=>{\n                    setIsTransferClassModalVisible(false);\n                    setSearchedTeacher(null);\n                },\n                onOk: handleTransferClass,\n                onSearchTeacher: handleSearchTeacher,\n                searchedTeacher: searchedTeacher,\n                hasAssistantTeacher: !!classInfo.assistantTeacherId,\n                onRemoveAssistant: handleRemoveAssistant,\n                loading: transferLoading\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                lineNumber: 2175,\n                columnNumber: 7\n            }, undefined),\n            isInviteCodeModalVisible && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg p-6 max-w-md w-full\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-between items-center mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-xl font-semibold\",\n                                    children: \"邀请码生成成功\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                    lineNumber: 2194,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"text-gray-500 hover:text-gray-700\",\n                                    onClick: ()=>setIsInviteCodeModalVisible(false),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                        className: \"h-6 w-6\",\n                                        fill: \"none\",\n                                        viewBox: \"0 0 24 24\",\n                                        stroke: \"currentColor\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 2,\n                                            d: \"M6 18L18 6M6 6l12 12\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                            lineNumber: 2200,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                        lineNumber: 2199,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                    lineNumber: 2195,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                            lineNumber: 2193,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between bg-gray-50 p-3 rounded-lg mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"font-mono text-lg text-blue-600 mr-4\",\n                                            children: inviteCode\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                            lineNumber: 2206,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: \"px-3 py-1 bg-blue-500 text-white rounded hover:bg-blue-600\",\n                                            onClick: ()=>{\n                                                navigator.clipboard.writeText(inviteCode).then(()=>alert(\"邀请码已复制\")).catch(()=>alert(\"复制失败，请手动复制\"));\n                                            },\n                                            children: \"复制\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                            lineNumber: 2207,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                    lineNumber: 2205,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-gray-500 text-sm space-y-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: \"您可以:\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                            lineNumber: 2219,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            className: \"list-disc list-inside space-y-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"将邀请码分享给学生，让他们加入班级\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                    lineNumber: 2221,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"邀请其他老师作为协助教师加入班级\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                    lineNumber: 2222,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                            lineNumber: 2220,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-yellow-500\",\n                                            children: \"⏰ 邀请码有效期为24小时\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                            lineNumber: 2224,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                    lineNumber: 2218,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                            lineNumber: 2204,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-end\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: \"px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600\",\n                                onClick: ()=>setIsInviteCodeModalVisible(false),\n                                children: \"关闭\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                lineNumber: 2230,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                            lineNumber: 2229,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                    lineNumber: 2192,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                lineNumber: 2191,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_teacher_space_components_modals__WEBPACK_IMPORTED_MODULE_6__.PublishTaskModal, {\n                visible: isPublishTaskModalVisible,\n                onCancel: ()=>setIsPublishTaskModalVisible(false),\n                onOk: handlePublishTask,\n                students: students,\n                selectedStudents: selectedStudentIds,\n                setSelectedStudents: setSelectedStudentIds,\n                fileList: fileList,\n                uploading: uploading,\n                handleUpload: handleUpload,\n                handleRemoveFile: handleRemoveFile,\n                displayTemplates: templates,\n                officialTemplates: officialTemplates,\n                selectedTemplateId: selectedTemplateId,\n                setSelectedTemplateId: setSelectedTemplateId\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                lineNumber: 2244,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_teacher_space_components_modals__WEBPACK_IMPORTED_MODULE_6__.ResetPasswordModal, {\n                visible: isResetPasswordModalVisible,\n                onCancel: ()=>setIsResetPasswordModalVisible(false),\n                onOk: handleResetPassword,\n                isBatch: false,\n                count: 1\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                lineNumber: 2262,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AssignBlocksModal__WEBPACK_IMPORTED_MODULE_7__.AssignBlocksModal, {\n                visible: isAssignBlocksModalVisible,\n                onCancel: ()=>{\n                    setIsAssignBlocksModalVisible(false);\n                    setSelectedStudentId(null);\n                    setSelectedStudentIds([]);\n                },\n                isClassCardAssign: selectedStudentId !== null,\n                loadingTemplates: loadingTemplates,\n                templates: templates,\n                studentTemplateUsage: studentTemplateUsage,\n                teacherTemplate: teacherTemplate,\n                onSelectTemplate: handleSelectTemplate,\n                onTemplateUsageClick: handleTemplateUsageClick,\n                userId: userId,\n                onRefreshTemplates: fetchTemplates,\n                students: students,\n                selectedStudentIds: selectedStudentIds,\n                userRoles: userRoles,\n                onSuccess: ()=>{\n                    refreshStudentList();\n                    fetchTemplateUsage();\n                },\n                onUpdateStudentTemplate: handleUpdateStudentTemplate\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                lineNumber: 2271,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AssignPointsModal__WEBPACK_IMPORTED_MODULE_9__.AssignPointsModal, {\n                visible: isAssignPointsModalVisible,\n                onCancel: ()=>{\n                    setIsAssignPointsModalVisible(false);\n                    setSelectedStudent(null);\n                },\n                // 根据 selectedStudent 是否存在来决定调用哪个处理函数\n                onOk: selectedStudent ? handleAssignPoints : handleBatchAssignPoints,\n                studentName: selectedStudent ? \"\".concat(selectedStudent.nickName) : \"已选择 \".concat(selectedStudentIds.length, \" 名学生\"),\n                studentId: (selectedStudent === null || selectedStudent === void 0 ? void 0 : selectedStudent.id) || 0,\n                userId: (selectedStudent === null || selectedStudent === void 0 ? void 0 : selectedStudent.userId) || 0,\n                student: selectedStudent,\n                isBatch: !selectedStudent,\n                selectedStudents: selectedStudentIds,\n                students: students,\n                onSuccess: ()=>{\n                // 移除这里的刷新，因为 onOk 内部已经处理了\n                },\n                refreshStudentList: refreshStudentList,\n                onGoToRedeemKey: (studentIds)=>{\n                    console.log(\"前往兑换密钥:\", studentIds);\n                    setIsBatchUseKeyPackageModalVisible(true);\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                lineNumber: 2298,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_BatchUseKeyPackageModal__WEBPACK_IMPORTED_MODULE_10__.BatchUseKeyPackageModal, {\n                open: isBatchUseKeyPackageModalVisible,\n                selectedStudentIds: selectedStudentIds,\n                students: students,\n                onClose: ()=>setIsBatchUseKeyPackageModalVisible(false),\n                onSuccess: handleBatchUseKeyPackageSuccess,\n                onGoToAssignPoints: handleGoToAssignPointsFromRedeem\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                lineNumber: 2324,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n        lineNumber: 1650,\n        columnNumber: 5\n    }, undefined);\n};\n_s(ClassDetail, \"rOoOqxdnOob2DP168DTJvyWgCZI=\", false, function() {\n    return [\n        _contexts_TemplateContext__WEBPACK_IMPORTED_MODULE_4__.useTemplate,\n        react_redux__WEBPACK_IMPORTED_MODULE_13__.useSelector,\n        react_redux__WEBPACK_IMPORTED_MODULE_13__.useSelector\n    ];\n});\n_c = ClassDetail;\n/* harmony default export */ __webpack_exports__[\"default\"] = (ClassDetail);\nvar _c;\n$RefreshReg$(_c, \"ClassDetail\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/workbench/components/ClassDetail.tsx\n"));

/***/ })

});