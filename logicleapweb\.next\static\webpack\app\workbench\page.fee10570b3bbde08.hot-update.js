"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/workbench/page",{

/***/ "(app-pages-browser)/./app/workbench/page.tsx":
/*!********************************!*\
  !*** ./app/workbench/page.tsx ***!
  \********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_LeftSidebar__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./components/LeftSidebar */ \"(app-pages-browser)/./app/workbench/components/LeftSidebar.tsx\");\n/* harmony import */ var _components_MainContent__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./components/MainContent */ \"(app-pages-browser)/./app/workbench/components/MainContent.tsx\");\n/* harmony import */ var _components_RightSidebar__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./components/RightSidebar */ \"(app-pages-browser)/./app/workbench/components/RightSidebar.tsx\");\n/* harmony import */ var _contexts_TemplateContext__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./contexts/TemplateContext */ \"(app-pages-browser)/./app/workbench/contexts/TemplateContext.tsx\");\n/* harmony import */ var _styles_workbench_css__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./styles/workbench.css */ \"(app-pages-browser)/./app/workbench/styles/workbench.css\");\n/* harmony import */ var _lib_api_points__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/api/points */ \"(app-pages-browser)/./lib/api/points.ts\");\n/* harmony import */ var _lib_api_teacher__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/api/teacher */ \"(app-pages-browser)/./lib/api/teacher.ts\");\n/* harmony import */ var react_redux__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! react-redux */ \"(app-pages-browser)/./node_modules/react-redux/dist/react-redux.mjs\");\n/* harmony import */ var _hooks_useUserInfo__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/hooks/useUserInfo */ \"(app-pages-browser)/./hooks/useUserInfo.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\nconst WorkbenchPage = ()=>{\n    _s();\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        studentCount: 0,\n        classCount: 0,\n        courseCount: 0\n    });\n    const [points, setPoints] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [activeView, setActiveView] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"快速开始\");\n    const [mounted, setMounted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedSchool, setSelectedSchool] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // 直接从Redux获取用户状态，并使用useUserInfo钩子\n    const userState = (0,react_redux__WEBPACK_IMPORTED_MODULE_10__.useSelector)((state)=>state.user.userState);\n    const userId = userState.userId;\n    // 使用useUserInfo hook获取refreshUserInfo函数\n    const { refreshUserInfo } = (0,_hooks_useUserInfo__WEBPACK_IMPORTED_MODULE_9__.useUserInfo)();\n    // 构建userInfo对象\n    const userInfo = {\n        id: userState.userId || 0,\n        gender: userState.gender || 0,\n        phone: userState.phone || \"\",\n        nickName: userState.nickName || \"\",\n        avatarUrl: userState.avatarUrl || \"\",\n        introduction: userState.introduction || \"\",\n        createTime: userState.createTime || \"\",\n        roleId: userState.roleId || 0,\n        role: userState.role || null,\n        roles: userState.roles || []\n    };\n    console.log(\"WorkbenchPage - userId:\", userId);\n    console.log(\"WorkbenchPage - userInfo:\", userInfo);\n    // 班级数据状态\n    const [classes, setClasses] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [classesLoading, setClassesLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [classesError, setClassesError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // 防止水合错误\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setMounted(true);\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const fetchData = async ()=>{\n            if (!userId || !mounted) return;\n            // 1. 刷新用户信息（包括头像）\n            try {\n                await refreshUserInfo();\n            } catch (error) {\n                console.error(\"Failed to refresh user info:\", error);\n            }\n            // 2. 获取教师统计数据\n            try {\n                const statsRes = await _lib_api_teacher__WEBPACK_IMPORTED_MODULE_8__[\"default\"].getTeacherStat(userId);\n                if (statsRes.code === 200) {\n                    setStats((prevStats)=>({\n                            ...prevStats,\n                            ...statsRes.data\n                        }));\n                }\n            } catch (error) {\n                console.error(\"Failed to fetch teacher stats:\", error);\n                // 使用占位数据\n                setStats((prevStats)=>({\n                        ...prevStats,\n                        studentCount: 258,\n                        classCount: 3,\n                        courseCount: 50\n                    }));\n            }\n            // 3. 获取能量点数\n            try {\n                const pointsRes = await _lib_api_points__WEBPACK_IMPORTED_MODULE_7__.pointsApi.getTotal();\n                if (pointsRes.data.code === 200) {\n                    const pointsData = pointsRes.data.data;\n                    const totalPoints = typeof pointsData === \"object\" && pointsData !== null && \"total\" in pointsData ? Number(pointsData.total) : Number(pointsData);\n                    setPoints(totalPoints || 0);\n                }\n            } catch (error) {\n                console.error(\"Failed to fetch points:\", error);\n                setPoints(9999);\n            }\n        };\n        fetchData();\n    }, [\n        userId,\n        mounted\n    ]);\n    const handleMenuItemClick = (itemName)=>{\n        setActiveView(itemName);\n    };\n    const handleSchoolSelect = (school)=>{\n        setSelectedSchool(school);\n        console.log(\"主页面接收到选中学校:\", school);\n    };\n    // 处理学校变化（从MainContent传回的回调）\n    const handleSchoolChange = (school)=>{\n        console.log(\"主页面接收到学校变化回调:\", school);\n    // 这里可以添加额外的学校变化处理逻辑\n    };\n    // 处理班级数据更新\n    const handleClassesUpdate = (classesData, loading, error)=>{\n        setClasses(classesData);\n        setClassesLoading(loading);\n        setClassesError(error);\n        console.log(\"主页面接收到班级数据:\", {\n            classesData,\n            loading,\n            error\n        });\n    };\n    // 防止水合错误，在客户端挂载前不渲染\n    if (!mounted) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"workbench-container\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"loading-placeholder\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"loading-spinner\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\page.tsx\",\n                        lineNumber: 146,\n                        columnNumber: 21\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: \"正在加载...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\page.tsx\",\n                        lineNumber: 147,\n                        columnNumber: 21\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\page.tsx\",\n                lineNumber: 145,\n                columnNumber: 17\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\page.tsx\",\n            lineNumber: 144,\n            columnNumber: 13\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_TemplateContext__WEBPACK_IMPORTED_MODULE_5__.TemplateProvider, {\n        userId: userId || null,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"workbench-container\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_LeftSidebar__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                    userInfo: userInfo,\n                    onMenuItemClick: handleMenuItemClick,\n                    onSchoolSelect: handleSchoolSelect,\n                    onClassesUpdate: handleClassesUpdate\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\page.tsx\",\n                    lineNumber: 156,\n                    columnNumber: 17\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_MainContent__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                    activeView: activeView,\n                    selectedSchool: selectedSchool,\n                    userInfo: userInfo,\n                    classes: classes,\n                    classesLoading: classesLoading,\n                    classesError: classesError,\n                    onClassesUpdate: (updatedClasses)=>handleClassesUpdate(updatedClasses, false, null),\n                    onSchoolChange: handleSchoolChange\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\page.tsx\",\n                    lineNumber: 162,\n                    columnNumber: 21\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_RightSidebar__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    userInfo: userInfo,\n                    stats: stats,\n                    points: points\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\page.tsx\",\n                    lineNumber: 172,\n                    columnNumber: 17\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\page.tsx\",\n            lineNumber: 155,\n            columnNumber: 13\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\page.tsx\",\n        lineNumber: 154,\n        columnNumber: 9\n    }, undefined);\n};\n_s(WorkbenchPage, \"lCk/ty9KoquWORi2R9hABhNGRQM=\", false, function() {\n    return [\n        react_redux__WEBPACK_IMPORTED_MODULE_10__.useSelector,\n        _hooks_useUserInfo__WEBPACK_IMPORTED_MODULE_9__.useUserInfo\n    ];\n});\n_c = WorkbenchPage;\n/* harmony default export */ __webpack_exports__[\"default\"] = (WorkbenchPage);\nvar _c;\n$RefreshReg$(_c, \"WorkbenchPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/workbench/page.tsx\n"));

/***/ })

});