"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/workbench/page",{

/***/ "(app-pages-browser)/./app/workbench/components/MainContent.tsx":
/*!**************************************************!*\
  !*** ./app/workbench/components/MainContent.tsx ***!
  \**************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _QuickActions__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./QuickActions */ \"(app-pages-browser)/./app/workbench/components/QuickActions.tsx\");\n/* harmony import */ var _OngoingTasks__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./OngoingTasks */ \"(app-pages-browser)/./app/workbench/components/OngoingTasks.tsx\");\n/* harmony import */ var _TemplateManagement__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./TemplateManagement */ \"(app-pages-browser)/./app/workbench/components/TemplateManagement.tsx\");\n/* harmony import */ var _ClassManagement__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./ClassManagement */ \"(app-pages-browser)/./app/workbench/components/ClassManagement.tsx\");\n/* harmony import */ var _ClassDetail__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./ClassDetail */ \"(app-pages-browser)/./app/workbench/components/ClassDetail.tsx\");\n/* harmony import */ var _ClassTasks__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./ClassTasks */ \"(app-pages-browser)/./app/workbench/components/ClassTasks.tsx\");\n/* harmony import */ var _CourseManagement__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./CourseManagement */ \"(app-pages-browser)/./app/workbench/components/CourseManagement.tsx\");\n/* harmony import */ var _ClassProjects__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./ClassProjects */ \"(app-pages-browser)/./app/workbench/components/ClassProjects.tsx\");\n/* harmony import */ var _SchoolSelectionModal__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./SchoolSelectionModal */ \"(app-pages-browser)/./app/workbench/components/SchoolSelectionModal.tsx\");\n/* harmony import */ var _ClassSelectionModal__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./ClassSelectionModal */ \"(app-pages-browser)/./app/workbench/components/ClassSelectionModal.tsx\");\n/* harmony import */ var _TemplateSelectionModal__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./TemplateSelectionModal */ \"(app-pages-browser)/./app/workbench/components/TemplateSelectionModal.tsx\");\n/* harmony import */ var _teacher_space_components_modals_create_class_modal__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ../../teacher-space/components/modals/create-class-modal */ \"(app-pages-browser)/./app/teacher-space/components/modals/create-class-modal.tsx\");\n/* harmony import */ var _lib_api_school__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/lib/api/school */ \"(app-pages-browser)/./lib/api/school.ts\");\n/* harmony import */ var _lib_api_class__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/lib/api/class */ \"(app-pages-browser)/./lib/api/class.ts\");\n/* harmony import */ var _barrel_optimize_names_Search_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Search!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_message_antd__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/message/index.js\");\n/* harmony import */ var _icon_park_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! @icon-park/react */ \"(app-pages-browser)/./node_modules/@icon-park/react/es/icons/HandUp.js\");\n/* harmony import */ var _icon_park_react_styles_index_css__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @icon-park/react/styles/index.css */ \"(app-pages-browser)/./node_modules/@icon-park/react/styles/index.css\");\n/* harmony import */ var _SchoolSelectionModal_css__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! ./SchoolSelectionModal.css */ \"(app-pages-browser)/./app/workbench/components/SchoolSelectionModal.css\");\n/* harmony import */ var _contexts_TemplateContext__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! ../contexts/TemplateContext */ \"(app-pages-browser)/./app/workbench/contexts/TemplateContext.tsx\");\n/* harmony import */ var _TemplateSelectionModal_css__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! ./TemplateSelectionModal.css */ \"(app-pages-browser)/./app/workbench/components/TemplateSelectionModal.css\");\n/* harmony import */ var _LeftSidebar__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! ./LeftSidebar */ \"(app-pages-browser)/./app/workbench/components/LeftSidebar.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst MainContent = (param)=>{\n    let { activeView = \"快速开始\", selectedSchool, userInfo, classes = [], classesLoading = false, classesError = null, onCloseDropdown, onClassesUpdate, onSchoolChange } = param;\n    _s();\n    const [isSchoolModalOpen, setIsSchoolModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isClassModalOpen, setIsClassModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isTemplateModalOpen, setIsTemplateModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isCreateClassModalOpen, setIsCreateClassModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [currentActionType, setCurrentActionType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [modalSelectedSchool, setModalSelectedSchool] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [selectedClass, setSelectedClass] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showClassDetail, setShowClassDetail] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedClassForDetail, setSelectedClassForDetail] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // 使用模板上下文\n    const { currentTemplate, globalTemplateChangeVersion } = (0,_contexts_TemplateContext__WEBPACK_IMPORTED_MODULE_18__.useTemplate)();\n    // 监听全局模板变化，通知所有班级详情组件刷新\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (globalTemplateChangeVersion > 0 && currentTemplate) {\n            console.log(\"MainContent - 检测到全局模板变化，版本号:\", globalTemplateChangeVersion);\n            console.log(\"MainContent - 新的当前模板:\", currentTemplate);\n        // 这里可以添加通知所有班级组件刷新的逻辑\n        // 由于班级详情组件已经在监听globalTemplateChangeVersion，\n        // 这里主要是为了确保状态同步\n        }\n    }, [\n        globalTemplateChangeVersion,\n        currentTemplate\n    ]);\n    // 监听学校选择变化，强制跳回班级管理页面\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (selectedSchool) {\n            console.log(\"检测到学校变化，强制跳回班级管理页面:\", selectedSchool);\n            // 重置班级详情显示状态，强制显示班级列表\n            setShowClassDetail(false);\n            setSelectedClassForDetail(null);\n            // 通知父组件学校变化\n            if (onSchoolChange) {\n                onSchoolChange(selectedSchool);\n            }\n        }\n    }, [\n        selectedSchool,\n        onSchoolChange\n    ]);\n    const handleQuickStartClick = async function() {\n        let actionType = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : \"发布任务\";\n        setCurrentActionType(actionType);\n        try {\n            // 获取用户的学校列表\n            const response = await _lib_api_school__WEBPACK_IMPORTED_MODULE_14__.schoolApi.getUserSchools();\n            if (response.data.code === 200) {\n                const schoolsData = response.data.data || [];\n                if (schoolsData.length === 1) {\n                    // 只有一个学校，直接选择并跳到班级选择\n                    setModalSelectedSchool(schoolsData[0]);\n                    setIsClassModalOpen(true);\n                } else if (schoolsData.length > 1) {\n                    // 多个学校，显示学校选择弹窗\n                    setIsSchoolModalOpen(true);\n                } else {\n                    // 没有学校，可以显示提示信息\n                    console.warn(\"用户没有关联的学校\");\n                }\n            }\n        } catch (error) {\n            console.error(\"获取学校列表失败:\", error);\n            // 出错时仍然显示学校选择弹窗\n            setIsSchoolModalOpen(true);\n        }\n    };\n    const handleCloseModal = ()=>{\n        setIsSchoolModalOpen(false);\n        setIsClassModalOpen(false);\n        setIsTemplateModalOpen(false);\n        setModalSelectedSchool(null);\n        setSelectedClass(null);\n        setCurrentActionType(\"\");\n    };\n    const handleSchoolSelect = (school)=>{\n        setModalSelectedSchool(school);\n        setIsSchoolModalOpen(false);\n        setIsClassModalOpen(true);\n    };\n    const handleClassSelect = (classData)=>{\n        setSelectedClass(classData);\n        setIsClassModalOpen(false);\n        setIsTemplateModalOpen(true);\n    };\n    const handleBackToSchool = ()=>{\n        setIsClassModalOpen(false);\n        setIsSchoolModalOpen(true);\n    };\n    const handleBackToClass = ()=>{\n        setIsTemplateModalOpen(false);\n        setIsClassModalOpen(true);\n    };\n    const handleClassClick = (classInfo)=>{\n        console.log(\"点击班级:\", classInfo);\n        setSelectedClassForDetail(classInfo);\n        setShowClassDetail(true);\n    };\n    const handleBackToClassManagement = ()=>{\n        setShowClassDetail(false);\n        setSelectedClassForDetail(null);\n    };\n    // 处理班级信息更新\n    const handleClassInfoUpdate = (updatedClassInfo)=>{\n        // 更新班级列表中对应的班级信息\n        const updatedClasses = classes.map((classItem)=>classItem.id === updatedClassInfo.id ? {\n                ...classItem,\n                ...updatedClassInfo\n            } : classItem);\n        // 更新缓存\n        if (selectedSchool) {\n            (0,_LeftSidebar__WEBPACK_IMPORTED_MODULE_20__.updateTeacherClassCache)(selectedSchool.id, updatedClasses);\n        }\n        // 通知父组件更新班级列表\n        onClassesUpdate === null || onClassesUpdate === void 0 ? void 0 : onClassesUpdate(updatedClasses);\n        // 同时更新当前选中的班级详情\n        setSelectedClassForDetail(updatedClassInfo);\n    };\n    // 处理班级删除\n    const handleClassDeleted = (deletedClassId)=>{\n        console.log(\"班级已删除:\", deletedClassId);\n        // 从班级列表中移除被删除的班级\n        const updatedClasses = classes.filter((cls)=>cls.id !== deletedClassId);\n        // 更新缓存\n        if (selectedSchool) {\n            (0,_LeftSidebar__WEBPACK_IMPORTED_MODULE_20__.updateTeacherClassCache)(selectedSchool.id, updatedClasses);\n        }\n        // 通知父组件更新班级列表\n        onClassesUpdate === null || onClassesUpdate === void 0 ? void 0 : onClassesUpdate(updatedClasses);\n    };\n    // 处理添加班级\n    const handleAddClass = ()=>{\n        if (!selectedSchool) {\n            _barrel_optimize_names_message_antd__WEBPACK_IMPORTED_MODULE_21__[\"default\"].error(\"请先选择学校\");\n            return;\n        }\n        setIsCreateClassModalOpen(true);\n    };\n    // 处理模板选择确认\n    const handleTemplateConfirm = (taskData)=>{\n        console.log(\"模板选择确认:\", taskData);\n        // 简单关闭弹窗，不执行具体的发布逻辑\n        handleCloseModal();\n    };\n    // 处理创建班级表单提交\n    const handleCreateClass = async (values)=>{\n        if (!selectedSchool) {\n            _barrel_optimize_names_message_antd__WEBPACK_IMPORTED_MODULE_21__[\"default\"].error(\"请先选择学校\");\n            return;\n        }\n        if (!(userInfo === null || userInfo === void 0 ? void 0 : userInfo.id)) {\n            _barrel_optimize_names_message_antd__WEBPACK_IMPORTED_MODULE_21__[\"default\"].error(\"用户信息不完整，请重新登录\");\n            return;\n        }\n        if (values.className.length > 8) {\n            _barrel_optimize_names_message_antd__WEBPACK_IMPORTED_MODULE_21__[\"default\"].error(\"班级名称不能超过8个字符\");\n            return;\n        }\n        try {\n            // 使用 createClass API，需要传递 teacherId\n            const response = await _lib_api_class__WEBPACK_IMPORTED_MODULE_15__.classApi.createClass(selectedSchool.id, values.className, userInfo.id);\n            if (response.data.code === 200) {\n                _barrel_optimize_names_message_antd__WEBPACK_IMPORTED_MODULE_21__[\"default\"].success(\"创建班级成功\");\n                setIsCreateClassModalOpen(false);\n                // 刷新班级列表并更新缓存\n                try {\n                    const classesResponse = await _lib_api_class__WEBPACK_IMPORTED_MODULE_15__.classApi.getTeacherClassesSimple(selectedSchool.id);\n                    if (classesResponse.data.code === 200) {\n                        const updatedClasses = classesResponse.data.data || [];\n                        // 更新缓存\n                        (0,_LeftSidebar__WEBPACK_IMPORTED_MODULE_20__.updateTeacherClassCache)(selectedSchool.id, updatedClasses);\n                        // 更新UI\n                        onClassesUpdate === null || onClassesUpdate === void 0 ? void 0 : onClassesUpdate(updatedClasses);\n                    }\n                } catch (error) {\n                    console.error(\"刷新班级列表失败:\", error);\n                }\n            } else {\n                _barrel_optimize_names_message_antd__WEBPACK_IMPORTED_MODULE_21__[\"default\"].error(response.data.message || \"该班级已存在或创建失败\");\n            }\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error(\"创建班级失败:\", error);\n            _barrel_optimize_names_message_antd__WEBPACK_IMPORTED_MODULE_21__[\"default\"].error(((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || \"创建班级失败\");\n        }\n    };\n    // 处理创建班级弹窗关闭\n    const handleCreateClassModalClose = ()=>{\n        setIsCreateClassModalOpen(false);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n        className: \"main-content relative\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"main-header\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"search-bar\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Search_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                className: \"search-icon\",\n                                size: 18\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\MainContent.tsx\",\n                                lineNumber: 299,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"text\",\n                                placeholder: \"搜索课程、任务或学生...\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\MainContent.tsx\",\n                                lineNumber: 300,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\MainContent.tsx\",\n                        lineNumber: 298,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        className: \"start-class-btn\",\n                        onClick: ()=>handleQuickStartClick(\"快速上课\"),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_icon_park_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                theme: \"filled\",\n                                size: 20,\n                                fill: [\n                                    \"#ffffff\"\n                                ],\n                                className: \"start-class-icon\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\MainContent.tsx\",\n                                lineNumber: 303,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: \"快速上课\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\MainContent.tsx\",\n                                lineNumber: 304,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\MainContent.tsx\",\n                        lineNumber: 302,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\MainContent.tsx\",\n                lineNumber: 297,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    activeView === \"模板管理\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_TemplateManagement__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        selectedSchool: selectedSchool,\n                        userInfo: userInfo\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\MainContent.tsx\",\n                        lineNumber: 309,\n                        columnNumber: 9\n                    }, undefined) : activeView === \"班级管理\" ? showClassDetail && selectedClassForDetail && selectedSchool ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ClassDetail__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                        classInfo: selectedClassForDetail,\n                        selectedSchool: selectedSchool,\n                        onBack: handleBackToClassManagement,\n                        onClassInfoUpdate: handleClassInfoUpdate,\n                        onClassDeleted: handleClassDeleted\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\MainContent.tsx\",\n                        lineNumber: 312,\n                        columnNumber: 11\n                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ClassManagement__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        selectedSchool: selectedSchool,\n                        userInfo: userInfo,\n                        classes: classes,\n                        classesLoading: classesLoading,\n                        classesError: classesError,\n                        onClassClick: handleClassClick,\n                        onCloseDropdown: onCloseDropdown,\n                        onAddClass: handleAddClass\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\MainContent.tsx\",\n                        lineNumber: 320,\n                        columnNumber: 11\n                    }, undefined) : activeView === \"班级任务\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ClassTasks__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\MainContent.tsx\",\n                        lineNumber: 332,\n                        columnNumber: 9\n                    }, undefined) : activeView === \"课程管理\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CourseManagement__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\MainContent.tsx\",\n                        lineNumber: 334,\n                        columnNumber: 9\n                    }, undefined) : activeView === \"班级项目\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ClassProjects__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\MainContent.tsx\",\n                        lineNumber: 336,\n                        columnNumber: 9\n                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_QuickActions__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\MainContent.tsx\",\n                                lineNumber: 339,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_OngoingTasks__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\MainContent.tsx\",\n                                lineNumber: 340,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_SchoolSelectionModal__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                        isOpen: isSchoolModalOpen,\n                        onClose: handleCloseModal,\n                        actionType: currentActionType,\n                        onSchoolSelect: handleSchoolSelect\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\MainContent.tsx\",\n                        lineNumber: 344,\n                        columnNumber: 7\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ClassSelectionModal__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                        isOpen: isClassModalOpen,\n                        onClose: handleCloseModal,\n                        onBack: handleBackToSchool,\n                        actionType: currentActionType,\n                        selectedSchool: modalSelectedSchool,\n                        onClassSelect: handleClassSelect\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\MainContent.tsx\",\n                        lineNumber: 351,\n                        columnNumber: 7\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_TemplateSelectionModal__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                        isOpen: isTemplateModalOpen,\n                        onClose: handleCloseModal,\n                        onBack: handleBackToClass,\n                        onConfirm: handleTemplateConfirm,\n                        actionType: currentActionType,\n                        selectedSchool: modalSelectedSchool,\n                        selectedClass: selectedClass\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\MainContent.tsx\",\n                        lineNumber: 360,\n                        columnNumber: 7\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_teacher_space_components_modals_create_class_modal__WEBPACK_IMPORTED_MODULE_13__.CreateClassModal, {\n                        visible: isCreateClassModalOpen,\n                        onCancel: handleCreateClassModalClose,\n                        onOk: handleCreateClass\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\MainContent.tsx\",\n                        lineNumber: 370,\n                        columnNumber: 7\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\MainContent.tsx\",\n                lineNumber: 307,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\MainContent.tsx\",\n        lineNumber: 296,\n        columnNumber: 5\n    }, undefined);\n};\n_s(MainContent, \"vuiDOKB9kf9CftAMGz7Fil9059k=\", false, function() {\n    return [\n        _contexts_TemplateContext__WEBPACK_IMPORTED_MODULE_18__.useTemplate\n    ];\n});\n_c = MainContent;\n/* harmony default export */ __webpack_exports__[\"default\"] = (MainContent);\nvar _c;\n$RefreshReg$(_c, \"MainContent\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/workbench/components/MainContent.tsx\n"));

/***/ })

});