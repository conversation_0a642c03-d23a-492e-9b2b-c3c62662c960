"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/workbench/page",{

/***/ "(app-pages-browser)/./app/workbench/components/MainContent.tsx":
/*!**************************************************!*\
  !*** ./app/workbench/components/MainContent.tsx ***!
  \**************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _QuickActions__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./QuickActions */ \"(app-pages-browser)/./app/workbench/components/QuickActions.tsx\");\n/* harmony import */ var _OngoingTasks__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./OngoingTasks */ \"(app-pages-browser)/./app/workbench/components/OngoingTasks.tsx\");\n/* harmony import */ var _TemplateManagement__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./TemplateManagement */ \"(app-pages-browser)/./app/workbench/components/TemplateManagement.tsx\");\n/* harmony import */ var _ClassManagement__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./ClassManagement */ \"(app-pages-browser)/./app/workbench/components/ClassManagement.tsx\");\n/* harmony import */ var _ClassDetail__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./ClassDetail */ \"(app-pages-browser)/./app/workbench/components/ClassDetail.tsx\");\n/* harmony import */ var _ClassTasks__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./ClassTasks */ \"(app-pages-browser)/./app/workbench/components/ClassTasks.tsx\");\n/* harmony import */ var _CourseManagement__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./CourseManagement */ \"(app-pages-browser)/./app/workbench/components/CourseManagement.tsx\");\n/* harmony import */ var _ClassProjects__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./ClassProjects */ \"(app-pages-browser)/./app/workbench/components/ClassProjects.tsx\");\n/* harmony import */ var _SchoolSelectionModal__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./SchoolSelectionModal */ \"(app-pages-browser)/./app/workbench/components/SchoolSelectionModal.tsx\");\n/* harmony import */ var _ClassSelectionModal__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./ClassSelectionModal */ \"(app-pages-browser)/./app/workbench/components/ClassSelectionModal.tsx\");\n/* harmony import */ var _TemplateSelectionModal__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./TemplateSelectionModal */ \"(app-pages-browser)/./app/workbench/components/TemplateSelectionModal.tsx\");\n/* harmony import */ var _teacher_space_components_modals_create_class_modal__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ../../teacher-space/components/modals/create-class-modal */ \"(app-pages-browser)/./app/teacher-space/components/modals/create-class-modal.tsx\");\n/* harmony import */ var _lib_api_school__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/lib/api/school */ \"(app-pages-browser)/./lib/api/school.ts\");\n/* harmony import */ var _lib_api_class__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/lib/api/class */ \"(app-pages-browser)/./lib/api/class.ts\");\n/* harmony import */ var _barrel_optimize_names_Search_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Search!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_message_antd__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/message/index.js\");\n/* harmony import */ var _icon_park_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! @icon-park/react */ \"(app-pages-browser)/./node_modules/@icon-park/react/es/icons/HandUp.js\");\n/* harmony import */ var _icon_park_react_styles_index_css__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @icon-park/react/styles/index.css */ \"(app-pages-browser)/./node_modules/@icon-park/react/styles/index.css\");\n/* harmony import */ var _SchoolSelectionModal_css__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! ./SchoolSelectionModal.css */ \"(app-pages-browser)/./app/workbench/components/SchoolSelectionModal.css\");\n/* harmony import */ var _contexts_TemplateContext__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! ../contexts/TemplateContext */ \"(app-pages-browser)/./app/workbench/contexts/TemplateContext.tsx\");\n/* harmony import */ var _TemplateSelectionModal_css__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! ./TemplateSelectionModal.css */ \"(app-pages-browser)/./app/workbench/components/TemplateSelectionModal.css\");\n/* harmony import */ var _LeftSidebar__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! ./LeftSidebar */ \"(app-pages-browser)/./app/workbench/components/LeftSidebar.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst MainContent = (param)=>{\n    let { activeView = \"快速开始\", selectedSchool, userInfo, classes = [], classesLoading = false, classesError = null, onCloseDropdown, onClassesUpdate, onSchoolChange } = param;\n    _s();\n    const [isSchoolModalOpen, setIsSchoolModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isClassModalOpen, setIsClassModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isTemplateModalOpen, setIsTemplateModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isCreateClassModalOpen, setIsCreateClassModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [currentActionType, setCurrentActionType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [modalSelectedSchool, setModalSelectedSchool] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [selectedClass, setSelectedClass] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showClassDetail, setShowClassDetail] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedClassForDetail, setSelectedClassForDetail] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // 使用模板上下文\n    const { currentTemplate, globalTemplateChangeVersion } = (0,_contexts_TemplateContext__WEBPACK_IMPORTED_MODULE_18__.useTemplate)();\n    // 监听全局模板变化，通知所有班级详情组件刷新\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (globalTemplateChangeVersion > 0 && currentTemplate) {\n            console.log(\"MainContent - 检测到全局模板变化，版本号:\", globalTemplateChangeVersion);\n            console.log(\"MainContent - 新的当前模板:\", currentTemplate);\n        // 这里可以添加通知所有班级组件刷新的逻辑\n        // 由于班级详情组件已经在监听globalTemplateChangeVersion，\n        // 这里主要是为了确保状态同步\n        }\n    }, [\n        globalTemplateChangeVersion,\n        currentTemplate\n    ]);\n    // 监听学校选择变化，强制跳回班级管理页面\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (selectedSchool) {\n            console.log(\"检测到学校变化，强制跳回班级管理页面:\", selectedSchool);\n            // 重置班级详情显示状态，强制显示班级列表\n            setShowClassDetail(false);\n            setSelectedClassForDetail(null);\n            // 通知父组件学校变化\n            if (onSchoolChange) {\n                onSchoolChange(selectedSchool);\n            }\n        }\n    }, [\n        selectedSchool,\n        onSchoolChange\n    ]);\n    const handleQuickStartClick = async function() {\n        let actionType = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : \"发布任务\";\n        setCurrentActionType(actionType);\n        try {\n            // 获取用户的学校列表\n            const response = await _lib_api_school__WEBPACK_IMPORTED_MODULE_14__.schoolApi.getUserSchools();\n            if (response.data.code === 200) {\n                const schoolsData = response.data.data || [];\n                if (schoolsData.length === 1) {\n                    // 只有一个学校，直接选择并跳到班级选择\n                    setModalSelectedSchool(schoolsData[0]);\n                    setIsClassModalOpen(true);\n                } else if (schoolsData.length > 1) {\n                    // 多个学校，显示学校选择弹窗\n                    setIsSchoolModalOpen(true);\n                } else {\n                    // 没有学校，可以显示提示信息\n                    console.warn(\"用户没有关联的学校\");\n                }\n            }\n        } catch (error) {\n            console.error(\"获取学校列表失败:\", error);\n            // 出错时仍然显示学校选择弹窗\n            setIsSchoolModalOpen(true);\n        }\n    };\n    const handleCloseModal = ()=>{\n        setIsSchoolModalOpen(false);\n        setIsClassModalOpen(false);\n        setIsTemplateModalOpen(false);\n        setModalSelectedSchool(null);\n        setSelectedClass(null);\n        setCurrentActionType(\"\");\n    };\n    const handleSchoolSelect = (school)=>{\n        setModalSelectedSchool(school);\n        setIsSchoolModalOpen(false);\n        setIsClassModalOpen(true);\n    };\n    const handleClassSelect = (classData)=>{\n        setSelectedClass(classData);\n        setIsClassModalOpen(false);\n        setIsTemplateModalOpen(true);\n    };\n    const handleBackToSchool = ()=>{\n        setIsClassModalOpen(false);\n        setIsSchoolModalOpen(true);\n    };\n    const handleBackToClass = ()=>{\n        setIsTemplateModalOpen(false);\n        setIsClassModalOpen(true);\n    };\n    const handleClassClick = (classInfo)=>{\n        console.log(\"点击班级:\", classInfo);\n        setSelectedClassForDetail(classInfo);\n        setShowClassDetail(true);\n    };\n    const handleBackToClassManagement = ()=>{\n        setShowClassDetail(false);\n        setSelectedClassForDetail(null);\n    };\n    // 处理班级信息更新\n    const handleClassInfoUpdate = (updatedClassInfo)=>{\n        // 更新班级列表中对应的班级信息\n        const updatedClasses = classes.map((classItem)=>classItem.id === updatedClassInfo.id ? {\n                ...classItem,\n                ...updatedClassInfo\n            } : classItem);\n        // 更新缓存\n        if (selectedSchool) {\n            (0,_LeftSidebar__WEBPACK_IMPORTED_MODULE_20__.updateTeacherClassCache)(selectedSchool.id, updatedClasses);\n        }\n        // 通知父组件更新班级列表\n        onClassesUpdate === null || onClassesUpdate === void 0 ? void 0 : onClassesUpdate(updatedClasses);\n        // 同时更新当前选中的班级详情\n        setSelectedClassForDetail(updatedClassInfo);\n    };\n    // 处理班级删除\n    const handleClassDeleted = (deletedClassId)=>{\n        console.log(\"班级已删除:\", deletedClassId);\n        // 从班级列表中移除被删除的班级\n        const updatedClasses = classes.filter((cls)=>cls.id !== deletedClassId);\n        // 更新缓存\n        if (selectedSchool) {\n            (0,_LeftSidebar__WEBPACK_IMPORTED_MODULE_20__.updateTeacherClassCache)(selectedSchool.id, updatedClasses);\n        }\n        // 通知父组件更新班级列表\n        onClassesUpdate === null || onClassesUpdate === void 0 ? void 0 : onClassesUpdate(updatedClasses);\n    };\n    // 处理添加班级\n    const handleAddClass = ()=>{\n        if (!selectedSchool) {\n            _barrel_optimize_names_message_antd__WEBPACK_IMPORTED_MODULE_21__[\"default\"].error(\"请先选择学校\");\n            return;\n        }\n        setIsCreateClassModalOpen(true);\n    };\n    // 处理模板选择确认\n    const handleTemplateConfirm = (taskData)=>{\n        console.log(\"模板选择确认:\", taskData);\n        // 简单关闭弹窗，不执行具体的发布逻辑\n        handleCloseModal();\n    };\n    // 处理创建班级表单提交\n    const handleCreateClass = async (values)=>{\n        if (!selectedSchool) {\n            _barrel_optimize_names_message_antd__WEBPACK_IMPORTED_MODULE_21__[\"default\"].error(\"请先选择学校\");\n            return;\n        }\n        if (!(userInfo === null || userInfo === void 0 ? void 0 : userInfo.id)) {\n            _barrel_optimize_names_message_antd__WEBPACK_IMPORTED_MODULE_21__[\"default\"].error(\"用户信息不完整，请重新登录\");\n            return;\n        }\n        if (values.className.length > 8) {\n            _barrel_optimize_names_message_antd__WEBPACK_IMPORTED_MODULE_21__[\"default\"].error(\"班级名称不能超过8个字符\");\n            return;\n        }\n        try {\n            // 使用 createClass API，需要传递 teacherId\n            const response = await _lib_api_class__WEBPACK_IMPORTED_MODULE_15__.classApi.createClass(selectedSchool.id, values.className, userInfo.id);\n            if (response.data.code === 200) {\n                _barrel_optimize_names_message_antd__WEBPACK_IMPORTED_MODULE_21__[\"default\"].success(\"创建班级成功\");\n                setIsCreateClassModalOpen(false);\n                // 刷新班级列表并更新缓存\n                try {\n                    const classesResponse = await _lib_api_class__WEBPACK_IMPORTED_MODULE_15__.classApi.getTeacherClassesSimple(selectedSchool.id);\n                    if (classesResponse.data.code === 200) {\n                        const updatedClasses = classesResponse.data.data || [];\n                        // 更新缓存\n                        (0,_LeftSidebar__WEBPACK_IMPORTED_MODULE_20__.updateTeacherClassCache)(selectedSchool.id, updatedClasses);\n                        // 更新UI\n                        onClassesUpdate === null || onClassesUpdate === void 0 ? void 0 : onClassesUpdate(updatedClasses);\n                    }\n                } catch (error) {\n                    console.error(\"刷新班级列表失败:\", error);\n                }\n            } else {\n                _barrel_optimize_names_message_antd__WEBPACK_IMPORTED_MODULE_21__[\"default\"].error(response.data.message || \"该班级已存在或创建失败\");\n            }\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error(\"创建班级失败:\", error);\n            _barrel_optimize_names_message_antd__WEBPACK_IMPORTED_MODULE_21__[\"default\"].error(((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || \"创建班级失败\");\n        }\n    };\n    // 处理创建班级弹窗关闭\n    const handleCreateClassModalClose = ()=>{\n        setIsCreateClassModalOpen(false);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n        className: \"main-content relative\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"main-header\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"search-bar\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Search_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                className: \"search-icon\",\n                                size: 18\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\MainContent.tsx\",\n                                lineNumber: 299,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"text\",\n                                placeholder: \"搜索课程、任务或学生...\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\MainContent.tsx\",\n                                lineNumber: 300,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\MainContent.tsx\",\n                        lineNumber: 298,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        className: \"start-class-btn\",\n                        onClick: ()=>handleQuickStartClick(\"快速上课\"),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_icon_park_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                theme: \"filled\",\n                                size: 20,\n                                fill: [\n                                    \"#ffffff\"\n                                ],\n                                className: \"start-class-icon\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\MainContent.tsx\",\n                                lineNumber: 303,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: \"快速上课\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\MainContent.tsx\",\n                                lineNumber: 304,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\MainContent.tsx\",\n                        lineNumber: 302,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\MainContent.tsx\",\n                lineNumber: 297,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"content-area\",\n                children: [\n                    \" \",\n                    activeView === \"模板管理\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_TemplateManagement__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        selectedSchool: selectedSchool,\n                        userInfo: userInfo\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\MainContent.tsx\",\n                        lineNumber: 309,\n                        columnNumber: 9\n                    }, undefined) : activeView === \"班级管理\" ? showClassDetail && selectedClassForDetail && selectedSchool ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ClassDetail__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                        classInfo: selectedClassForDetail,\n                        selectedSchool: selectedSchool,\n                        onBack: handleBackToClassManagement,\n                        onClassInfoUpdate: handleClassInfoUpdate,\n                        onClassDeleted: handleClassDeleted\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\MainContent.tsx\",\n                        lineNumber: 312,\n                        columnNumber: 11\n                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ClassManagement__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        selectedSchool: selectedSchool,\n                        userInfo: userInfo,\n                        classes: classes,\n                        classesLoading: classesLoading,\n                        classesError: classesError,\n                        onClassClick: handleClassClick,\n                        onCloseDropdown: onCloseDropdown,\n                        onAddClass: handleAddClass\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\MainContent.tsx\",\n                        lineNumber: 320,\n                        columnNumber: 11\n                    }, undefined) : activeView === \"班级任务\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ClassTasks__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\MainContent.tsx\",\n                        lineNumber: 332,\n                        columnNumber: 9\n                    }, undefined) : activeView === \"课程管理\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CourseManagement__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\MainContent.tsx\",\n                        lineNumber: 334,\n                        columnNumber: 9\n                    }, undefined) : activeView === \"班级项目\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ClassProjects__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\MainContent.tsx\",\n                        lineNumber: 336,\n                        columnNumber: 9\n                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_QuickActions__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\MainContent.tsx\",\n                                lineNumber: 339,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_OngoingTasks__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\MainContent.tsx\",\n                                lineNumber: 340,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_SchoolSelectionModal__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                        isOpen: isSchoolModalOpen,\n                        onClose: handleCloseModal,\n                        actionType: currentActionType,\n                        onSchoolSelect: handleSchoolSelect\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\MainContent.tsx\",\n                        lineNumber: 344,\n                        columnNumber: 7\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ClassSelectionModal__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                        isOpen: isClassModalOpen,\n                        onClose: handleCloseModal,\n                        onBack: handleBackToSchool,\n                        actionType: currentActionType,\n                        selectedSchool: modalSelectedSchool,\n                        onClassSelect: handleClassSelect\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\MainContent.tsx\",\n                        lineNumber: 351,\n                        columnNumber: 7\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_TemplateSelectionModal__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                        isOpen: isTemplateModalOpen,\n                        onClose: handleCloseModal,\n                        onBack: handleBackToClass,\n                        onConfirm: handleTemplateConfirm,\n                        actionType: currentActionType,\n                        selectedSchool: modalSelectedSchool,\n                        selectedClass: selectedClass\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\MainContent.tsx\",\n                        lineNumber: 360,\n                        columnNumber: 7\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_teacher_space_components_modals_create_class_modal__WEBPACK_IMPORTED_MODULE_13__.CreateClassModal, {\n                        visible: isCreateClassModalOpen,\n                        onCancel: handleCreateClassModalClose,\n                        onOk: handleCreateClass\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\MainContent.tsx\",\n                        lineNumber: 370,\n                        columnNumber: 7\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\MainContent.tsx\",\n                lineNumber: 307,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\MainContent.tsx\",\n        lineNumber: 296,\n        columnNumber: 5\n    }, undefined);\n};\n_s(MainContent, \"vuiDOKB9kf9CftAMGz7Fil9059k=\", false, function() {\n    return [\n        _contexts_TemplateContext__WEBPACK_IMPORTED_MODULE_18__.useTemplate\n    ];\n});\n_c = MainContent;\n/* harmony default export */ __webpack_exports__[\"default\"] = (MainContent);\nvar _c;\n$RefreshReg$(_c, \"MainContent\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/workbench/components/MainContent.tsx\n"));

/***/ })

});