"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/workbench/page",{

/***/ "(app-pages-browser)/./app/workbench/components/ClassDetail.css":
/*!**************************************************!*\
  !*** ./app/workbench/components/ClassDetail.css ***!
  \**************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony default export */ __webpack_exports__[\"default\"] = (\"7e2f421e119e\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2FwcC93b3JrYmVuY2gvY29tcG9uZW50cy9DbGFzc0RldGFpbC5jc3MiLCJtYXBwaW5ncyI6IjtBQUFBLCtEQUFlLGNBQWM7QUFDN0IsSUFBSSxJQUFVLElBQUksaUJBQWlCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL2FwcC93b3JrYmVuY2gvY29tcG9uZW50cy9DbGFzc0RldGFpbC5jc3M/ZTcyOSJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjdlMmY0MjFlMTE5ZVwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/workbench/components/ClassDetail.css\n"));

/***/ }),

/***/ "(app-pages-browser)/./app/workbench/styles/workbench.css":
/*!********************************************!*\
  !*** ./app/workbench/styles/workbench.css ***!
  \********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony default export */ __webpack_exports__[\"default\"] = (\"96f5d0f99b45\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2FwcC93b3JrYmVuY2gvc3R5bGVzL3dvcmtiZW5jaC5jc3MiLCJtYXBwaW5ncyI6IjtBQUFBLCtEQUFlLGNBQWM7QUFDN0IsSUFBSSxJQUFVLElBQUksaUJBQWlCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL2FwcC93b3JrYmVuY2gvc3R5bGVzL3dvcmtiZW5jaC5jc3M/NThlZSJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjk2ZjVkMGY5OWI0NVwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/workbench/styles/workbench.css\n"));

/***/ }),

/***/ "(app-pages-browser)/./app/workbench/components/RightSidebar.tsx":
/*!***************************************************!*\
  !*** ./app/workbench/components/RightSidebar.tsx ***!
  \***************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Bell_MessageSquare_MoreHorizontal_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,MessageSquare,MoreHorizontal!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bell.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_MessageSquare_MoreHorizontal_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,MessageSquare,MoreHorizontal!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/message-square.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_MessageSquare_MoreHorizontal_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,MessageSquare,MoreHorizontal!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/ellipsis.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nconst RightSidebar = (param)=>{\n    let { userInfo, stats, points } = param;\n    const courses = [\n        {\n            name: \"人工智能启蒙课\",\n            series: \"4课时系列\"\n        },\n        {\n            name: \"机器学习基础\",\n            series: \"8课时系列\"\n        },\n        {\n            name: \"深度学习入门\",\n            series: \"12课时系列\"\n        },\n        {\n            name: \"编程思维训练\",\n            series: \"6课时系列\"\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"aside\", {\n        className: \"bg-white flex flex-col h-full overflow-hidden border-l border-[#eef0f2]\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mx-3 mt-3 rounded-lg flex-shrink-0\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"px-3 pt-3 pb-2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"user-info flex justify-between items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"header-icons flex gap-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_MessageSquare_MoreHorizontal_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                            className: \"text-gray-500 w-4 h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\RightSidebar.tsx\",\n                                            lineNumber: 33,\n                                            columnNumber: 29\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_MessageSquare_MoreHorizontal_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                            className: \"text-gray-500 w-4 h-4 scale-x-[-1]\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\RightSidebar.tsx\",\n                                            lineNumber: 34,\n                                            columnNumber: 29\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\RightSidebar.tsx\",\n                                    lineNumber: 32,\n                                    columnNumber: 25\n                                }, undefined),\n                                !userInfo.nickName && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>{\n                                        const currentPath = window.location.pathname;\n                                        window.location.href = \"/login?redirect=\".concat(encodeURIComponent(currentPath));\n                                    },\n                                    className: \"login-btn bg-blue-500 hover:bg-blue-600 text-white rounded-full px-4 py-1.5 transition-colors duration-200 text-xs font-medium\",\n                                    children: \"前往登录\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\RightSidebar.tsx\",\n                                    lineNumber: 37,\n                                    columnNumber: 29\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\RightSidebar.tsx\",\n                            lineNumber: 31,\n                            columnNumber: 21\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\RightSidebar.tsx\",\n                        lineNumber: 30,\n                        columnNumber: 17\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                            className: \"stats-card\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-sm font-medium text-gray-800 mb-4 text-center\",\n                                    children: \"数据概览\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\RightSidebar.tsx\",\n                                    lineNumber: 53,\n                                    columnNumber: 25\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"stats-grid grid grid-cols-3 w-full text-center mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"stat-value text-gray-800 font-bold text-lg\",\n                                                    children: userInfo.nickName ? stats.studentCount || \"258\" : \"0\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\RightSidebar.tsx\",\n                                                    lineNumber: 57,\n                                                    columnNumber: 33\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"stat-label text-xs text-gray-500\",\n                                                    children: \"总学生数\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\RightSidebar.tsx\",\n                                                    lineNumber: 58,\n                                                    columnNumber: 33\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\RightSidebar.tsx\",\n                                            lineNumber: 56,\n                                            columnNumber: 29\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"stat-value text-cyan-400 font-bold text-lg\",\n                                                    children: userInfo.nickName ? stats.classCount || \"3\" : \"0\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\RightSidebar.tsx\",\n                                                    lineNumber: 61,\n                                                    columnNumber: 33\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"stat-label text-xs text-gray-500\",\n                                                    children: \"总班级数\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\RightSidebar.tsx\",\n                                                    lineNumber: 62,\n                                                    columnNumber: 33\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\RightSidebar.tsx\",\n                                            lineNumber: 60,\n                                            columnNumber: 29\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"stat-value text-gray-800 font-bold text-lg\",\n                                                    children: userInfo.nickName ? stats.courseCount || \"50\" : \"0\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\RightSidebar.tsx\",\n                                                    lineNumber: 65,\n                                                    columnNumber: 33\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"stat-label text-xs text-gray-500\",\n                                                    children: \"总课程数\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\RightSidebar.tsx\",\n                                                    lineNumber: 66,\n                                                    columnNumber: 33\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\RightSidebar.tsx\",\n                                            lineNumber: 64,\n                                            columnNumber: 29\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\RightSidebar.tsx\",\n                                    lineNumber: 55,\n                                    columnNumber: 25\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"energy-balance flex items-center justify-between w-full px-1 pt-2 border-t border-gray-100\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-col\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-500 text-xs mb-0 leading-none whitespace-nowrap\",\n                                                    children: \"剩余能量\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\RightSidebar.tsx\",\n                                                    lineNumber: 72,\n                                                    columnNumber: 33\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"energy-value text-gray-800 font-bold text-lg -mt-1 leading-none\",\n                                                    children: userInfo.nickName ? points || \"9,999\" : \"0\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\RightSidebar.tsx\",\n                                                    lineNumber: 73,\n                                                    columnNumber: 33\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\RightSidebar.tsx\",\n                                            lineNumber: 71,\n                                            columnNumber: 29\n                                        }, undefined),\n                                        userInfo.nickName && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: \"recharge-btn bg-blue-500 hover:bg-blue-600 text-white rounded-md px-3 py-1.5 ml-3 transition-colors duration-200 text-xs font-medium whitespace-nowrap\",\n                                            children: \"充值\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\RightSidebar.tsx\",\n                                            lineNumber: 76,\n                                            columnNumber: 33\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\RightSidebar.tsx\",\n                                    lineNumber: 70,\n                                    columnNumber: 25\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\RightSidebar.tsx\",\n                            lineNumber: 52,\n                            columnNumber: 21\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\RightSidebar.tsx\",\n                        lineNumber: 51,\n                        columnNumber: 17\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\RightSidebar.tsx\",\n                lineNumber: 28,\n                columnNumber: 13\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mx-3 mt-3 mb-3 rounded-lg p-3 flex-1 flex flex-col min-h-0\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"my-courses-card flex-1 flex flex-col min-h-0\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"card-header flex justify-between items-center mb-3 flex-shrink-0\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"text-gray-800 font-medium text-sm\",\n                                        children: \"我的课程\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\RightSidebar.tsx\",\n                                        lineNumber: 89,\n                                        columnNumber: 25\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_MessageSquare_MoreHorizontal_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        size: 16,\n                                        className: \"text-gray-400\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\RightSidebar.tsx\",\n                                        lineNumber: 90,\n                                        columnNumber: 25\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\RightSidebar.tsx\",\n                                lineNumber: 88,\n                                columnNumber: 21\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"courses-list-container flex-1 overflow-y-auto min-h-0\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"courses-list space-y-2\",\n                                    children: courses.map((course, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            className: \"course-item flex items-center p-2 rounded-lg hover:bg-gray-50 transition-colors\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"course-icon bg-gradient-to-br from-blue-500 to-purple-600 w-8 h-8 rounded-lg mr-3 flex-shrink-0 flex items-center justify-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-4 h-4 text-white\",\n                                                        fill: \"currentColor\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                d: \"M12 2L2 7v10c0 5.55 3.84 9.74 9 11 5.16-1.26 9-5.45 9-11V7l-10-5z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\RightSidebar.tsx\",\n                                                                lineNumber: 98,\n                                                                columnNumber: 45\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                d: \"M9 12l2 2 4-4\",\n                                                                stroke: \"currentColor\",\n                                                                strokeWidth: \"2\",\n                                                                fill: \"none\",\n                                                                strokeLinecap: \"round\",\n                                                                strokeLinejoin: \"round\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\RightSidebar.tsx\",\n                                                                lineNumber: 99,\n                                                                columnNumber: 45\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\RightSidebar.tsx\",\n                                                        lineNumber: 97,\n                                                        columnNumber: 41\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\RightSidebar.tsx\",\n                                                    lineNumber: 96,\n                                                    columnNumber: 37\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"course-details flex-grow min-w-0\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"course-name text-xs font-medium text-gray-800 truncate\",\n                                                            children: course.name\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\RightSidebar.tsx\",\n                                                            lineNumber: 103,\n                                                            columnNumber: 41\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"course-series text-xs text-gray-500\",\n                                                            children: course.series\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\RightSidebar.tsx\",\n                                                            lineNumber: 104,\n                                                            columnNumber: 41\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\RightSidebar.tsx\",\n                                                    lineNumber: 102,\n                                                    columnNumber: 37\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: \"draft-btn text-xs text-green-600 bg-green-50 hover:bg-green-100 border border-green-200 px-2 py-1 rounded-md transition-colors duration-200 font-medium ml-2 whitespace-nowrap\",\n                                                    children: \"草稿\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\RightSidebar.tsx\",\n                                                    lineNumber: 106,\n                                                    columnNumber: 37\n                                                }, undefined)\n                                            ]\n                                        }, index, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\RightSidebar.tsx\",\n                                            lineNumber: 95,\n                                            columnNumber: 33\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\RightSidebar.tsx\",\n                                    lineNumber: 93,\n                                    columnNumber: 25\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\RightSidebar.tsx\",\n                                lineNumber: 92,\n                                columnNumber: 21\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\RightSidebar.tsx\",\n                        lineNumber: 87,\n                        columnNumber: 17\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"current-template-card bg-gray-50 rounded-lg p-3 relative mt-3 flex-shrink-0\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-4 h-4 text-gray-500\",\n                                    width: \"24\",\n                                    height: \"24\",\n                                    viewBox: \"0 0 48 48\",\n                                    fill: \"none\",\n                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            d: \"M42 19H5.99998\",\n                                            stroke: \"currentColor\",\n                                            strokeWidth: \"4\",\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\RightSidebar.tsx\",\n                                            lineNumber: 117,\n                                            columnNumber: 29\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            d: \"M30 7L42 19\",\n                                            stroke: \"currentColor\",\n                                            strokeWidth: \"4\",\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\RightSidebar.tsx\",\n                                            lineNumber: 118,\n                                            columnNumber: 29\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            d: \"M6.79897 29H42.799\",\n                                            stroke: \"currentColor\",\n                                            strokeWidth: \"4\",\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\RightSidebar.tsx\",\n                                            lineNumber: 119,\n                                            columnNumber: 29\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            d: \"M6.79895 29L18.799 41\",\n                                            stroke: \"currentColor\",\n                                            strokeWidth: \"4\",\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\RightSidebar.tsx\",\n                                            lineNumber: 120,\n                                            columnNumber: 29\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\RightSidebar.tsx\",\n                                    lineNumber: 116,\n                                    columnNumber: 25\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-500 text-xs\",\n                                            children: \"当前模板\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\RightSidebar.tsx\",\n                                            lineNumber: 123,\n                                            columnNumber: 29\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-800 text-xs font-medium\",\n                                            children: \"系统默认全权限模板\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\RightSidebar.tsx\",\n                                            lineNumber: 124,\n                                            columnNumber: 29\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\RightSidebar.tsx\",\n                                    lineNumber: 122,\n                                    columnNumber: 25\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\RightSidebar.tsx\",\n                            lineNumber: 115,\n                            columnNumber: 21\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\RightSidebar.tsx\",\n                        lineNumber: 114,\n                        columnNumber: 17\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\RightSidebar.tsx\",\n                lineNumber: 86,\n                columnNumber: 13\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\RightSidebar.tsx\",\n        lineNumber: 26,\n        columnNumber: 9\n    }, undefined);\n};\n_c = RightSidebar;\n/* harmony default export */ __webpack_exports__[\"default\"] = (RightSidebar);\nvar _c;\n$RefreshReg$(_c, \"RightSidebar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/workbench/components/RightSidebar.tsx\n"));

/***/ })

});