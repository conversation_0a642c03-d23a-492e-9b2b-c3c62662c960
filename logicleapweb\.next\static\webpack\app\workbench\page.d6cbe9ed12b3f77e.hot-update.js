"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/workbench/page",{

/***/ "(app-pages-browser)/./app/workbench/components/MainContent.tsx":
/*!**************************************************!*\
  !*** ./app/workbench/components/MainContent.tsx ***!
  \**************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _QuickActions__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./QuickActions */ \"(app-pages-browser)/./app/workbench/components/QuickActions.tsx\");\n/* harmony import */ var _OngoingTasks__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./OngoingTasks */ \"(app-pages-browser)/./app/workbench/components/OngoingTasks.tsx\");\n/* harmony import */ var _TemplateManagement__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./TemplateManagement */ \"(app-pages-browser)/./app/workbench/components/TemplateManagement.tsx\");\n/* harmony import */ var _ClassManagement__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./ClassManagement */ \"(app-pages-browser)/./app/workbench/components/ClassManagement.tsx\");\n/* harmony import */ var _ClassDetail__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./ClassDetail */ \"(app-pages-browser)/./app/workbench/components/ClassDetail.tsx\");\n/* harmony import */ var _ClassTasks__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./ClassTasks */ \"(app-pages-browser)/./app/workbench/components/ClassTasks.tsx\");\n/* harmony import */ var _CourseManagement__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./CourseManagement */ \"(app-pages-browser)/./app/workbench/components/CourseManagement.tsx\");\n/* harmony import */ var _ClassProjects__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./ClassProjects */ \"(app-pages-browser)/./app/workbench/components/ClassProjects.tsx\");\n/* harmony import */ var _SchoolSelectionModal__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./SchoolSelectionModal */ \"(app-pages-browser)/./app/workbench/components/SchoolSelectionModal.tsx\");\n/* harmony import */ var _ClassSelectionModal__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./ClassSelectionModal */ \"(app-pages-browser)/./app/workbench/components/ClassSelectionModal.tsx\");\n/* harmony import */ var _TemplateSelectionModal__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./TemplateSelectionModal */ \"(app-pages-browser)/./app/workbench/components/TemplateSelectionModal.tsx\");\n/* harmony import */ var _teacher_space_components_modals_create_class_modal__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ../../teacher-space/components/modals/create-class-modal */ \"(app-pages-browser)/./app/teacher-space/components/modals/create-class-modal.tsx\");\n/* harmony import */ var _lib_api_school__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/lib/api/school */ \"(app-pages-browser)/./lib/api/school.ts\");\n/* harmony import */ var _lib_api_class__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/lib/api/class */ \"(app-pages-browser)/./lib/api/class.ts\");\n/* harmony import */ var _barrel_optimize_names_Search_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Search!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_message_antd__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/message/index.js\");\n/* harmony import */ var _icon_park_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! @icon-park/react */ \"(app-pages-browser)/./node_modules/@icon-park/react/es/icons/HandUp.js\");\n/* harmony import */ var _icon_park_react_styles_index_css__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @icon-park/react/styles/index.css */ \"(app-pages-browser)/./node_modules/@icon-park/react/styles/index.css\");\n/* harmony import */ var _SchoolSelectionModal_css__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! ./SchoolSelectionModal.css */ \"(app-pages-browser)/./app/workbench/components/SchoolSelectionModal.css\");\n/* harmony import */ var _contexts_TemplateContext__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! ../contexts/TemplateContext */ \"(app-pages-browser)/./app/workbench/contexts/TemplateContext.tsx\");\n/* harmony import */ var _TemplateSelectionModal_css__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! ./TemplateSelectionModal.css */ \"(app-pages-browser)/./app/workbench/components/TemplateSelectionModal.css\");\n/* harmony import */ var _LeftSidebar__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! ./LeftSidebar */ \"(app-pages-browser)/./app/workbench/components/LeftSidebar.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst MainContent = (param)=>{\n    let { activeView = \"快速开始\", selectedSchool, userInfo, classes = [], classesLoading = false, classesError = null, onCloseDropdown, onClassesUpdate, onSchoolChange } = param;\n    _s();\n    const [isSchoolModalOpen, setIsSchoolModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isClassModalOpen, setIsClassModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isTemplateModalOpen, setIsTemplateModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isCreateClassModalOpen, setIsCreateClassModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [currentActionType, setCurrentActionType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [modalSelectedSchool, setModalSelectedSchool] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [selectedClass, setSelectedClass] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showClassDetail, setShowClassDetail] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedClassForDetail, setSelectedClassForDetail] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // 使用模板上下文\n    const { currentTemplate, globalTemplateChangeVersion } = (0,_contexts_TemplateContext__WEBPACK_IMPORTED_MODULE_18__.useTemplate)();\n    // 监听全局模板变化，通知所有班级详情组件刷新\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (globalTemplateChangeVersion > 0 && currentTemplate) {\n            console.log(\"MainContent - 检测到全局模板变化，版本号:\", globalTemplateChangeVersion);\n            console.log(\"MainContent - 新的当前模板:\", currentTemplate);\n        // 这里可以添加通知所有班级组件刷新的逻辑\n        // 由于班级详情组件已经在监听globalTemplateChangeVersion，\n        // 这里主要是为了确保状态同步\n        }\n    }, [\n        globalTemplateChangeVersion,\n        currentTemplate\n    ]);\n    // 监听学校选择变化，强制跳回班级管理页面\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (selectedSchool) {\n            console.log(\"检测到学校变化，强制跳回班级管理页面:\", selectedSchool);\n            // 重置班级详情显示状态，强制显示班级列表\n            setShowClassDetail(false);\n            setSelectedClassForDetail(null);\n            // 通知父组件学校变化\n            if (onSchoolChange) {\n                onSchoolChange(selectedSchool);\n            }\n        }\n    }, [\n        selectedSchool,\n        onSchoolChange\n    ]);\n    const handleQuickStartClick = async function() {\n        let actionType = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : \"发布任务\";\n        setCurrentActionType(actionType);\n        try {\n            // 获取用户的学校列表\n            const response = await _lib_api_school__WEBPACK_IMPORTED_MODULE_14__.schoolApi.getUserSchools();\n            if (response.data.code === 200) {\n                const schoolsData = response.data.data || [];\n                if (schoolsData.length === 1) {\n                    // 只有一个学校，直接选择并跳到班级选择\n                    setModalSelectedSchool(schoolsData[0]);\n                    setIsClassModalOpen(true);\n                } else if (schoolsData.length > 1) {\n                    // 多个学校，显示学校选择弹窗\n                    setIsSchoolModalOpen(true);\n                } else {\n                    // 没有学校，可以显示提示信息\n                    console.warn(\"用户没有关联的学校\");\n                }\n            }\n        } catch (error) {\n            console.error(\"获取学校列表失败:\", error);\n            // 出错时仍然显示学校选择弹窗\n            setIsSchoolModalOpen(true);\n        }\n    };\n    const handleCloseModal = ()=>{\n        setIsSchoolModalOpen(false);\n        setIsClassModalOpen(false);\n        setIsTemplateModalOpen(false);\n        setModalSelectedSchool(null);\n        setSelectedClass(null);\n        setCurrentActionType(\"\");\n    };\n    const handleSchoolSelect = (school)=>{\n        setModalSelectedSchool(school);\n        setIsSchoolModalOpen(false);\n        setIsClassModalOpen(true);\n    };\n    const handleClassSelect = (classData)=>{\n        setSelectedClass(classData);\n        setIsClassModalOpen(false);\n        setIsTemplateModalOpen(true);\n    };\n    const handleBackToSchool = ()=>{\n        setIsClassModalOpen(false);\n        setIsSchoolModalOpen(true);\n    };\n    const handleBackToClass = ()=>{\n        setIsTemplateModalOpen(false);\n        setIsClassModalOpen(true);\n    };\n    const handleClassClick = (classInfo)=>{\n        console.log(\"点击班级:\", classInfo);\n        setSelectedClassForDetail(classInfo);\n        setShowClassDetail(true);\n    };\n    const handleBackToClassManagement = ()=>{\n        setShowClassDetail(false);\n        setSelectedClassForDetail(null);\n    };\n    // 处理班级信息更新\n    const handleClassInfoUpdate = (updatedClassInfo)=>{\n        // 更新班级列表中对应的班级信息\n        const updatedClasses = classes.map((classItem)=>classItem.id === updatedClassInfo.id ? {\n                ...classItem,\n                ...updatedClassInfo\n            } : classItem);\n        // 更新缓存\n        if (selectedSchool) {\n            (0,_LeftSidebar__WEBPACK_IMPORTED_MODULE_20__.updateTeacherClassCache)(selectedSchool.id, updatedClasses);\n        }\n        // 通知父组件更新班级列表\n        onClassesUpdate === null || onClassesUpdate === void 0 ? void 0 : onClassesUpdate(updatedClasses);\n        // 同时更新当前选中的班级详情\n        setSelectedClassForDetail(updatedClassInfo);\n    };\n    // 处理班级删除\n    const handleClassDeleted = (deletedClassId)=>{\n        console.log(\"班级已删除:\", deletedClassId);\n        // 从班级列表中移除被删除的班级\n        const updatedClasses = classes.filter((cls)=>cls.id !== deletedClassId);\n        // 更新缓存\n        if (selectedSchool) {\n            (0,_LeftSidebar__WEBPACK_IMPORTED_MODULE_20__.updateTeacherClassCache)(selectedSchool.id, updatedClasses);\n        }\n        // 通知父组件更新班级列表\n        onClassesUpdate === null || onClassesUpdate === void 0 ? void 0 : onClassesUpdate(updatedClasses);\n    };\n    // 处理添加班级\n    const handleAddClass = ()=>{\n        if (!selectedSchool) {\n            _barrel_optimize_names_message_antd__WEBPACK_IMPORTED_MODULE_21__[\"default\"].error(\"请先选择学校\");\n            return;\n        }\n        setIsCreateClassModalOpen(true);\n    };\n    // 处理模板选择确认\n    const handleTemplateConfirm = (taskData)=>{\n        console.log(\"模板选择确认:\", taskData);\n        // 简单关闭弹窗，不执行具体的发布逻辑\n        handleCloseModal();\n    };\n    // 处理创建班级表单提交\n    const handleCreateClass = async (values)=>{\n        if (!selectedSchool) {\n            _barrel_optimize_names_message_antd__WEBPACK_IMPORTED_MODULE_21__[\"default\"].error(\"请先选择学校\");\n            return;\n        }\n        if (!(userInfo === null || userInfo === void 0 ? void 0 : userInfo.id)) {\n            _barrel_optimize_names_message_antd__WEBPACK_IMPORTED_MODULE_21__[\"default\"].error(\"用户信息不完整，请重新登录\");\n            return;\n        }\n        if (values.className.length > 8) {\n            _barrel_optimize_names_message_antd__WEBPACK_IMPORTED_MODULE_21__[\"default\"].error(\"班级名称不能超过8个字符\");\n            return;\n        }\n        try {\n            // 使用 createClass API，需要传递 teacherId\n            const response = await _lib_api_class__WEBPACK_IMPORTED_MODULE_15__.classApi.createClass(selectedSchool.id, values.className, userInfo.id);\n            if (response.data.code === 200) {\n                _barrel_optimize_names_message_antd__WEBPACK_IMPORTED_MODULE_21__[\"default\"].success(\"创建班级成功\");\n                setIsCreateClassModalOpen(false);\n                // 刷新班级列表并更新缓存\n                try {\n                    const classesResponse = await _lib_api_class__WEBPACK_IMPORTED_MODULE_15__.classApi.getTeacherClassesSimple(selectedSchool.id);\n                    if (classesResponse.data.code === 200) {\n                        const updatedClasses = classesResponse.data.data || [];\n                        // 更新缓存\n                        (0,_LeftSidebar__WEBPACK_IMPORTED_MODULE_20__.updateTeacherClassCache)(selectedSchool.id, updatedClasses);\n                        // 更新UI\n                        onClassesUpdate === null || onClassesUpdate === void 0 ? void 0 : onClassesUpdate(updatedClasses);\n                    }\n                } catch (error) {\n                    console.error(\"刷新班级列表失败:\", error);\n                }\n            } else {\n                _barrel_optimize_names_message_antd__WEBPACK_IMPORTED_MODULE_21__[\"default\"].error(response.data.message || \"该班级已存在或创建失败\");\n            }\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error(\"创建班级失败:\", error);\n            _barrel_optimize_names_message_antd__WEBPACK_IMPORTED_MODULE_21__[\"default\"].error(((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || \"创建班级失败\");\n        }\n    };\n    // 处理创建班级弹窗关闭\n    const handleCreateClassModalClose = ()=>{\n        setIsCreateClassModalOpen(false);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n        className: \"main-content relative\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"main-header\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"search-bar\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Search_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                className: \"search-icon\",\n                                size: 18\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\MainContent.tsx\",\n                                lineNumber: 299,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"text\",\n                                placeholder: \"搜索课程、任务或学生...\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\MainContent.tsx\",\n                                lineNumber: 300,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\MainContent.tsx\",\n                        lineNumber: 298,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        className: \"start-class-btn\",\n                        onClick: ()=>handleQuickStartClick(\"快速上课\"),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_icon_park_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                theme: \"filled\",\n                                size: 20,\n                                fill: [\n                                    \"#ffffff\"\n                                ],\n                                className: \"start-class-icon\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\MainContent.tsx\",\n                                lineNumber: 303,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: \"快速上课\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\MainContent.tsx\",\n                                lineNumber: 304,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\MainContent.tsx\",\n                        lineNumber: 302,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\MainContent.tsx\",\n                lineNumber: 297,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"content-area\",\n                style: {\n                    padding: \"24px\"\n                },\n                children: [\n                    \" \",\n                    activeView === \"模板管理\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_TemplateManagement__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        selectedSchool: selectedSchool,\n                        userInfo: userInfo\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\MainContent.tsx\",\n                        lineNumber: 309,\n                        columnNumber: 9\n                    }, undefined) : activeView === \"班级管理\" ? showClassDetail && selectedClassForDetail && selectedSchool ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ClassDetail__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                        classInfo: selectedClassForDetail,\n                        selectedSchool: selectedSchool,\n                        onBack: handleBackToClassManagement,\n                        onClassInfoUpdate: handleClassInfoUpdate,\n                        onClassDeleted: handleClassDeleted\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\MainContent.tsx\",\n                        lineNumber: 312,\n                        columnNumber: 11\n                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ClassManagement__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        selectedSchool: selectedSchool,\n                        userInfo: userInfo,\n                        classes: classes,\n                        classesLoading: classesLoading,\n                        classesError: classesError,\n                        onClassClick: handleClassClick,\n                        onCloseDropdown: onCloseDropdown,\n                        onAddClass: handleAddClass\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\MainContent.tsx\",\n                        lineNumber: 320,\n                        columnNumber: 11\n                    }, undefined) : activeView === \"班级任务\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ClassTasks__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\MainContent.tsx\",\n                        lineNumber: 332,\n                        columnNumber: 9\n                    }, undefined) : activeView === \"课程管理\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CourseManagement__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\MainContent.tsx\",\n                        lineNumber: 334,\n                        columnNumber: 9\n                    }, undefined) : activeView === \"班级项目\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ClassProjects__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\MainContent.tsx\",\n                        lineNumber: 336,\n                        columnNumber: 9\n                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_QuickActions__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\MainContent.tsx\",\n                                lineNumber: 339,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_OngoingTasks__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\MainContent.tsx\",\n                                lineNumber: 340,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_SchoolSelectionModal__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                        isOpen: isSchoolModalOpen,\n                        onClose: handleCloseModal,\n                        actionType: currentActionType,\n                        onSchoolSelect: handleSchoolSelect\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\MainContent.tsx\",\n                        lineNumber: 344,\n                        columnNumber: 7\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ClassSelectionModal__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                        isOpen: isClassModalOpen,\n                        onClose: handleCloseModal,\n                        onBack: handleBackToSchool,\n                        actionType: currentActionType,\n                        selectedSchool: modalSelectedSchool,\n                        onClassSelect: handleClassSelect\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\MainContent.tsx\",\n                        lineNumber: 351,\n                        columnNumber: 7\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_TemplateSelectionModal__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                        isOpen: isTemplateModalOpen,\n                        onClose: handleCloseModal,\n                        onBack: handleBackToClass,\n                        onConfirm: handleTemplateConfirm,\n                        actionType: currentActionType,\n                        selectedSchool: modalSelectedSchool,\n                        selectedClass: selectedClass\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\MainContent.tsx\",\n                        lineNumber: 360,\n                        columnNumber: 7\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_teacher_space_components_modals_create_class_modal__WEBPACK_IMPORTED_MODULE_13__.CreateClassModal, {\n                        visible: isCreateClassModalOpen,\n                        onCancel: handleCreateClassModalClose,\n                        onOk: handleCreateClass\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\MainContent.tsx\",\n                        lineNumber: 370,\n                        columnNumber: 7\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\MainContent.tsx\",\n                lineNumber: 307,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\MainContent.tsx\",\n        lineNumber: 296,\n        columnNumber: 5\n    }, undefined);\n};\n_s(MainContent, \"vuiDOKB9kf9CftAMGz7Fil9059k=\", false, function() {\n    return [\n        _contexts_TemplateContext__WEBPACK_IMPORTED_MODULE_18__.useTemplate\n    ];\n});\n_c = MainContent;\n/* harmony default export */ __webpack_exports__[\"default\"] = (MainContent);\nvar _c;\n$RefreshReg$(_c, \"MainContent\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/workbench/components/MainContent.tsx\n"));

/***/ })

});