"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/workbench/page",{

/***/ "(app-pages-browser)/./app/workbench/components/ClassDetail.css":
/*!**************************************************!*\
  !*** ./app/workbench/components/ClassDetail.css ***!
  \**************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony default export */ __webpack_exports__[\"default\"] = (\"f0cb931f1323\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2FwcC93b3JrYmVuY2gvY29tcG9uZW50cy9DbGFzc0RldGFpbC5jc3MiLCJtYXBwaW5ncyI6IjtBQUFBLCtEQUFlLGNBQWM7QUFDN0IsSUFBSSxJQUFVLElBQUksaUJBQWlCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL2FwcC93b3JrYmVuY2gvY29tcG9uZW50cy9DbGFzc0RldGFpbC5jc3M/ZTcyOSJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcImYwY2I5MzFmMTMyM1wiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/workbench/components/ClassDetail.css\n"));

/***/ }),

/***/ "(app-pages-browser)/./app/workbench/components/ClassDetail.tsx":
/*!**************************************************!*\
  !*** ./app/workbench/components/ClassDetail.tsx ***!
  \**************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Blocks_ChevronDown_Download_Edit_Gift_Link_Plus_Search_Settings_Trash2_Upload_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Blocks,ChevronDown,Download,Edit,Gift,Link,Plus,Search,Settings,Trash2,Upload,UserPlus,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Blocks_ChevronDown_Download_Edit_Gift_Link_Plus_Search_Settings_Trash2_Upload_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Blocks,ChevronDown,Download,Edit,Gift,Link,Plus,Search,Settings,Trash2,Upload,UserPlus,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Blocks_ChevronDown_Download_Edit_Gift_Link_Plus_Search_Settings_Trash2_Upload_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Blocks,ChevronDown,Download,Edit,Gift,Link,Plus,Search,Settings,Trash2,Upload,UserPlus,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Blocks_ChevronDown_Download_Edit_Gift_Link_Plus_Search_Settings_Trash2_Upload_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Blocks,ChevronDown,Download,Edit,Gift,Link,Plus,Search,Settings,Trash2,Upload,UserPlus,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user-plus.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Blocks_ChevronDown_Download_Edit_Gift_Link_Plus_Search_Settings_Trash2_Upload_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Blocks,ChevronDown,Download,Edit,Gift,Link,Plus,Search,Settings,Trash2,Upload,UserPlus,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Blocks_ChevronDown_Download_Edit_Gift_Link_Plus_Search_Settings_Trash2_Upload_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Blocks,ChevronDown,Download,Edit,Gift,Link,Plus,Search,Settings,Trash2,Upload,UserPlus,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Blocks_ChevronDown_Download_Edit_Gift_Link_Plus_Search_Settings_Trash2_Upload_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Blocks,ChevronDown,Download,Edit,Gift,Link,Plus,Search,Settings,Trash2,Upload,UserPlus,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Blocks_ChevronDown_Download_Edit_Gift_Link_Plus_Search_Settings_Trash2_Upload_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Blocks,ChevronDown,Download,Edit,Gift,Link,Plus,Search,Settings,Trash2,Upload,UserPlus,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/link.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Blocks_ChevronDown_Download_Edit_Gift_Link_Plus_Search_Settings_Trash2_Upload_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Blocks,ChevronDown,Download,Edit,Gift,Link,Plus,Search,Settings,Trash2,Upload,UserPlus,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/gift.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Blocks_ChevronDown_Download_Edit_Gift_Link_Plus_Search_Settings_Trash2_Upload_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Blocks,ChevronDown,Download,Edit,Gift,Link,Plus,Search,Settings,Trash2,Upload,UserPlus,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/blocks.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Blocks_ChevronDown_Download_Edit_Gift_Link_Plus_Search_Settings_Trash2_Upload_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Blocks,ChevronDown,Download,Edit,Gift,Link,Plus,Search,Settings,Trash2,Upload,UserPlus,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Blocks_ChevronDown_Download_Edit_Gift_Link_Plus_Search_Settings_Trash2_Upload_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Blocks,ChevronDown,Download,Edit,Gift,Link,Plus,Search,Settings,Trash2,Upload,UserPlus,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Blocks_ChevronDown_Download_Edit_Gift_Link_Plus_Search_Settings_Trash2_Upload_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Blocks,ChevronDown,Download,Edit,Gift,Link,Plus,Search,Settings,Trash2,Upload,UserPlus,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Blocks_ChevronDown_Download_Edit_Gift_Link_Plus_Search_Settings_Trash2_Upload_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Blocks,ChevronDown,Download,Edit,Gift,Link,Plus,Search,Settings,Trash2,Upload,UserPlus,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Blocks_ChevronDown_Download_Edit_Gift_Link_Plus_Search_Settings_Trash2_Upload_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Blocks,ChevronDown,Download,Edit,Gift,Link,Plus,Search,Settings,Trash2,Upload,UserPlus,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _lib_api_class__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../lib/api/class */ \"(app-pages-browser)/./lib/api/class.ts\");\n/* harmony import */ var _lib_api_student__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../lib/api/student */ \"(app-pages-browser)/./lib/api/student.ts\");\n/* harmony import */ var react_redux__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! react-redux */ \"(app-pages-browser)/./node_modules/react-redux/dist/react-redux.mjs\");\n/* harmony import */ var _contexts_TemplateContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../contexts/TemplateContext */ \"(app-pages-browser)/./app/workbench/contexts/TemplateContext.tsx\");\n/* harmony import */ var _AddStudentModal__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./AddStudentModal */ \"(app-pages-browser)/./app/workbench/components/AddStudentModal.tsx\");\n/* harmony import */ var _teacher_space_components_modals__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../teacher-space/components/modals */ \"(app-pages-browser)/./app/teacher-space/components/modals/index.ts\");\n/* harmony import */ var _AssignBlocksModal__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./AssignBlocksModal */ \"(app-pages-browser)/./app/workbench/components/AssignBlocksModal.tsx\");\n/* harmony import */ var _TransferClassModal__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./TransferClassModal */ \"(app-pages-browser)/./app/workbench/components/TransferClassModal.tsx\");\n/* harmony import */ var _AssignPointsModal__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./AssignPointsModal */ \"(app-pages-browser)/./app/workbench/components/AssignPointsModal.tsx\");\n/* harmony import */ var _BatchUseKeyPackageModal__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./BatchUseKeyPackageModal */ \"(app-pages-browser)/./app/workbench/components/BatchUseKeyPackageModal.tsx\");\n/* harmony import */ var _StudentList__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./StudentList */ \"(app-pages-browser)/./app/workbench/components/StudentList/index.tsx\");\n/* harmony import */ var _ClassDetail_css__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./ClassDetail.css */ \"(app-pages-browser)/./app/workbench/components/ClassDetail.css\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// 类型定义已移至 ./types/index.ts\nconst ClassDetail = (param)=>{\n    let { classInfo: initialClassInfo, selectedSchool, onBack, onClassInfoUpdate, onClassDeleted } = param;\n    var _selectedStudent_nickName;\n    _s();\n    // 创建内部状态来管理班级信息，这样可以在编辑后更新显示\n    const [classInfo, setClassInfo] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(initialClassInfo);\n    const [students, setStudents] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [selectedStudent, setSelectedStudent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // 同步外部传入的classInfo变化\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setClassInfo(initialClassInfo);\n    }, [\n        initialClassInfo\n    ]);\n    const [isAddStudentModalVisible, setIsAddStudentModalVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isSettingsDropdownOpen, setIsSettingsDropdownOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isBatchActionsDropdownOpen, setIsBatchActionsDropdownOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const dropdownRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const settingsDropdownRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const batchActionsDropdownRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // 模态框状态\n    const [isEditClassModalVisible, setIsEditClassModalVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isImportStudentModalVisible, setIsImportStudentModalVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isTransferClassModalVisible, setIsTransferClassModalVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isInviteCodeModalVisible, setIsInviteCodeModalVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isAssignBlocksModalVisible, setIsAssignBlocksModalVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [inviteCode, setInviteCode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    // 新增功能模态框状态\n    const [isPublishTaskModalVisible, setIsPublishTaskModalVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isResetPasswordModalVisible, setIsResetPasswordModalVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isAssignPointsModalVisible, setIsAssignPointsModalVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isBatchUseKeyPackageModalVisible, setIsBatchUseKeyPackageModalVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // const [isRedeemKeyModalVisible, setIsRedeemKeyModalVisible] = useState(false); // 未使用，已移除\n    // 转让管理相关状态\n    const [searchedTeacher, setSearchedTeacher] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [transferLoading, setTransferLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // PublishTaskModal 相关状态\n    const [fileList, setFileList] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [uploading, setUploading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [templates, setTemplates] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [officialTemplates] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]); // 保留以避免引用错误\n    const [selectedTemplateId, setSelectedTemplateId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // AssignBlocksModal 相关状态\n    const [loadingTemplates, setLoadingTemplates] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [studentTemplateUsage, setStudentTemplateUsage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [teacherTemplate, setTeacherTemplate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    const [selectedStudentId, setSelectedStudentId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // 添加userRoles状态，与teacher-space保持一致\n    const [userRoles, setUserRoles] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // 多选状态\n    const [selectedStudentIds, setSelectedStudentIds] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isSelectAll, setIsSelectAll] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // 使用全局模板状态\n    const { currentTemplate, globalTemplateChangeVersion, refreshCurrentTemplate } = (0,_contexts_TemplateContext__WEBPACK_IMPORTED_MODULE_4__.useTemplate)();\n    // 全局当前模板信息（用于没有个人模板的学生）\n    const [globalCurrentTemplate, setGlobalCurrentTemplate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // 获取教师当前模板（从班级接口获取）\n    const fetchTeacherCurrentTemplate = async ()=>{\n        try {\n            if (!(classInfo === null || classInfo === void 0 ? void 0 : classInfo.id) || !(classInfo === null || classInfo === void 0 ? void 0 : classInfo.schoolId)) return null;\n            const response = await _lib_api_class__WEBPACK_IMPORTED_MODULE_2__.classApi.getTeacherClasses(classInfo.schoolId, userId);\n            if (response.data.code === 200 && response.data.data) {\n                // 找到当前班级的模板信息\n                const currentClass = response.data.data.find((cls)=>cls.id === classInfo.id);\n                if (currentClass && currentClass.templateName) {\n                    const templateInfo = {\n                        templateId: currentClass.templateId || 0,\n                        templateName: currentClass.templateName,\n                        isOfficial: currentClass.isOfficial || false\n                    };\n                    setGlobalCurrentTemplate(templateInfo);\n                    console.log(\"获取到教师当前模板:\", templateInfo);\n                    return templateInfo;\n                }\n            }\n        } catch (error) {\n            console.error(\"获取教师当前模板失败:\", error);\n        }\n        return null;\n    };\n    // 同步教师模板到学生（当教师模板更新时调用）\n    const syncTeacherTemplateToStudents = async ()=>{\n        try {\n            const newTemplate = await fetchTeacherCurrentTemplate();\n            if (newTemplate && students.length > 0) {\n                console.log(\"同步教师模板到学生:\", newTemplate);\n                // 使用 ref 获取最新的 personalTemplateAssignments\n                const currentPersonalAssignments = personalTemplateAssignmentsRef.current;\n                // 更新所有没有个人分配模板的学生\n                setStudents((prevStudents)=>prevStudents.map((student)=>{\n                        // 如果学生有个人分配的模板，保持不变\n                        if (currentPersonalAssignments.has(student.userId)) {\n                            console.log(\"保持学生 \".concat(student.nickName, \" 的个人模板 (syncTeacherTemplateToStudents)\"));\n                            return student;\n                        }\n                        // 否则更新为教师当前模板\n                        console.log(\"更新学生 \".concat(student.nickName, \" 为教师模板 (syncTeacherTemplateToStudents)\"));\n                        return {\n                            ...student,\n                            currentTemplate: newTemplate\n                        };\n                    }));\n            }\n        } catch (error) {\n            console.error(\"同步教师模板失败:\", error);\n        }\n    };\n    // 存储个人分配的模板信息，避免被fetchStudents覆盖\n    const [personalTemplateAssignments, setPersonalTemplateAssignments] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Map());\n    // 添加强制重新渲染的状态\n    const [renderVersion] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0); // setRenderVersion暂时未使用\n    // 获取用户信息\n    const userId = (0,react_redux__WEBPACK_IMPORTED_MODULE_13__.useSelector)((state)=>state.user.userState.userId);\n    const roleId = (0,react_redux__WEBPACK_IMPORTED_MODULE_13__.useSelector)((state)=>state.user.userState.roleId);\n    // 获取班级学生数据\n    const fetchStudents = async ()=>{\n        if (!(classInfo === null || classInfo === void 0 ? void 0 : classInfo.id)) return;\n        try {\n            setLoading(true);\n            setError(null);\n            // 并行获取班级学生基础数据和教师当前模板\n            const [response] = await Promise.all([\n                _lib_api_class__WEBPACK_IMPORTED_MODULE_2__.classApi.getClassStudents(classInfo.id),\n                fetchTeacherCurrentTemplate()\n            ]);\n            if (response.data.code === 200) {\n                const studentsData = response.data.data || [];\n                if (studentsData.length === 0) {\n                    setStudents([]);\n                    setSelectedStudent(null);\n                    // 如果学生数量变为0，也要更新班级信息\n                    if (classInfo.studentCount !== 0) {\n                        const updatedClassInfo = {\n                            ...classInfo,\n                            studentCount: 0\n                        };\n                        setClassInfo(updatedClassInfo);\n                        // 通知父组件更新班级列表中的学生数\n                        onClassInfoUpdate === null || onClassInfoUpdate === void 0 ? void 0 : onClassInfoUpdate(updatedClassInfo);\n                    }\n                    return;\n                }\n                // 提取学生ID列表\n                const userIds = studentsData.map((s)=>s.userId);\n                // 批量获取学生详细信息（包括模板和能量信息）\n                const studentInfoMap = await (userIds.length > 20 ? Promise.all(Array.from({\n                    length: Math.ceil(userIds.length / 20)\n                }, (_, i)=>userIds.slice(i * 20, (i + 1) * 20)).map((batchId)=>_lib_api_student__WEBPACK_IMPORTED_MODULE_3__.studentApi.getStudentsBatchInfo(batchId).then((param)=>{\n                        let { data: { data } } = param;\n                        return data;\n                    }).catch((error)=>{\n                        console.error(\"获取学生批量信息失败:\", error);\n                        return {};\n                    }))).then((results)=>Object.assign({}, ...results)) : _lib_api_student__WEBPACK_IMPORTED_MODULE_3__.studentApi.getStudentsBatchInfo(userIds).then((param)=>{\n                    let { data: { data } } = param;\n                    return data;\n                }).catch((error)=>{\n                    console.error(\"获取学生批量信息失败:\", error);\n                    return {};\n                }));\n                // 合并学生数据\n                const completeStudents = studentsData.map((s)=>{\n                    const studentInfo = studentInfoMap[s.userId];\n                    // 检查是否有个人分配的模板\n                    const personalTemplate = personalTemplateAssignments.get(s.userId);\n                    return {\n                        ...s,\n                        ...studentInfo,\n                        id: s.userId,\n                        nickName: s.nickName || \"学生\".concat(s.studentNumber || s.userId),\n                        totalPoints: (studentInfo === null || studentInfo === void 0 ? void 0 : studentInfo.totalPoints) || 0,\n                        availablePoints: (studentInfo === null || studentInfo === void 0 ? void 0 : studentInfo.availablePoints) || 0,\n                        avatarUrl: s.avatarUrl || \"/default-avatar.png\",\n                        // 优先级：个人分配的模板 > 学生API返回的模板 > 教师当前模板 > null\n                        currentTemplate: (()=>{\n                            var _studentsData_;\n                            const result = personalTemplate || (studentInfo === null || studentInfo === void 0 ? void 0 : studentInfo.currentTemplate) || globalCurrentTemplate;\n                            // 调试信息\n                            if (s.userId === ((_studentsData_ = studentsData[0]) === null || _studentsData_ === void 0 ? void 0 : _studentsData_.userId)) {\n                                console.log(\"学生模板分配逻辑:\", {\n                                    studentId: s.userId,\n                                    studentName: s.nickName,\n                                    personalTemplate,\n                                    studentApiTemplate: studentInfo === null || studentInfo === void 0 ? void 0 : studentInfo.currentTemplate,\n                                    globalCurrentTemplate,\n                                    finalTemplate: result\n                                });\n                            }\n                            return result;\n                        })()\n                    };\n                });\n                setStudents(completeStudents);\n                // 如果学生数量发生变化，更新班级信息并通知父组件\n                if (completeStudents.length !== classInfo.studentCount) {\n                    const updatedClassInfo = {\n                        ...classInfo,\n                        studentCount: completeStudents.length\n                    };\n                    setClassInfo(updatedClassInfo);\n                    // 通知父组件更新班级列表中的学生数\n                    onClassInfoUpdate === null || onClassInfoUpdate === void 0 ? void 0 : onClassInfoUpdate(updatedClassInfo);\n                }\n                // 设置userRoles，与teacher-space保持一致\n                const newUserRoles = completeStudents.map((student)=>({\n                        userId: student.userId,\n                        roleId: 1 // 学生角色ID为1\n                    }));\n                // 添加教师自己的角色\n                newUserRoles.push({\n                    userId: userId,\n                    roleId: 2 // 教师角色ID为2\n                });\n                setUserRoles(newUserRoles);\n                // 默认选择第一个学生\n                if (completeStudents.length > 0) {\n                    setSelectedStudent(completeStudents[0]);\n                }\n            } else {\n                setError(response.data.message || \"获取学生列表失败\");\n            }\n        } catch (err) {\n            console.error(\"获取学生列表失败:\", err);\n            console.error(\"错误详情:\", {\n                message: err instanceof Error ? err.message : \"未知错误\",\n                stack: err instanceof Error ? err.stack : undefined,\n                classId: classInfo.id\n            });\n            setError(\"获取学生列表失败，请稍后重试\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    // 获取权限模板列表\n    const fetchTemplates = async ()=>{\n        setLoadingTemplates(true);\n        try {\n            const { getRoleTemplateList, getOfficialTemplates } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! ../../../lib/api/role */ \"(app-pages-browser)/./lib/api/role.ts\"));\n            // 同时获取教师自定义模板和官方模板\n            const [customResponse, officialResponse] = await Promise.all([\n                getRoleTemplateList(userId),\n                getOfficialTemplates()\n            ]);\n            if (customResponse.data.code === 200 && officialResponse.data.code === 200) {\n                const customTemplates = customResponse.data.data || [];\n                const officialTemplates = officialResponse.data.data || [];\n                // 为官方模板添加标记\n                const markedOfficialTemplates = officialTemplates.map((template)=>({\n                        ...template,\n                        isOfficial: true\n                    }));\n                // 合并所有模板\n                const allTemplates = [\n                    ...customTemplates,\n                    ...markedOfficialTemplates\n                ];\n                setTemplates(allTemplates);\n            }\n        } catch (error) {\n            console.error(\"获取权限模板列表失败:\", error);\n        } finally{\n            setLoadingTemplates(false);\n        }\n    };\n    // 获取模板使用情况\n    const fetchTemplateUsage = async ()=>{\n        try {\n            const { getUserCurrentTemplate, getStudentTemplates } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! ../../../lib/api/role */ \"(app-pages-browser)/./lib/api/role.ts\"));\n            // 获取教师使用的模板\n            const teacherResponse = await getUserCurrentTemplate(userId);\n            if (teacherResponse.data.code === 200) {\n                const teacherTemplateData = teacherResponse.data.data;\n                setTeacherTemplate(teacherTemplateData);\n            // 同时更新当前模板状态，确保与模板管理同步\n            // 暂时注释掉以停止循环\n            /*\r\n        if (teacherTemplateData) {\r\n          // 使用refreshCurrentTemplate来更新全局模板状态\r\n          await refreshCurrentTemplate();\r\n        }\r\n        */ }\n            // 获取学生使用的模板\n            const studentResponse = await getStudentTemplates({\n                teacherId: userId,\n                page: 1,\n                size: 200\n            });\n            if (studentResponse.data.code === 200) {\n                // 统计每个模板被使用的次数\n                const usage = {};\n                studentResponse.data.data.list.forEach((item)=>{\n                    if (item.templateId) {\n                        usage[item.templateId] = (usage[item.templateId] || 0) + 1;\n                    }\n                });\n                setStudentTemplateUsage(usage);\n            }\n        } catch (error) {\n            console.error(\"获取模板使用情况失败:\", error);\n        }\n    };\n    // 组件挂载时获取学生数据和模板\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        fetchStudents();\n        fetchTemplates();\n        fetchTemplateUsage();\n    }, [\n        classInfo === null || classInfo === void 0 ? void 0 : classInfo.id,\n        userId\n    ]);\n    // 当组件重新挂载或班级变化时，确保获取最新的当前模板\n    // 暂时注释掉以停止循环\n    /*\r\n  useEffect(() => {\r\n    if (classInfo?.id && userId) {\r\n      refreshCurrentTemplate();\r\n    }\r\n  }, [classInfo?.id, userId]);\r\n  */ // 移除这个useEffect，避免在currentTemplate变化时覆盖个人分配的模板\n    // 使用 useRef 来保存最新的 personalTemplateAssignments\n    const personalTemplateAssignmentsRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(personalTemplateAssignments);\n    personalTemplateAssignmentsRef.current = personalTemplateAssignments;\n    // 监听全局模板变化，重新获取教师当前模板并更新学生数据\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (globalTemplateChangeVersion > 0 && (classInfo === null || classInfo === void 0 ? void 0 : classInfo.id)) {\n            console.log(\"检测到模板变化，重新获取教师当前模板\");\n            // 重新获取教师当前模板\n            fetchTeacherCurrentTemplate().then((newTemplate)=>{\n                if (newTemplate && students.length > 0) {\n                    // 不清除个人分配的模板记录，保留学生的个人模板数据\n                    // setPersonalTemplateAssignments(new Map()); // 注释掉这行\n                    // 使用 ref 获取最新的 personalTemplateAssignments\n                    const currentPersonalAssignments = personalTemplateAssignmentsRef.current;\n                    // 只更新没有个人模板的学生为新的教师当前模板\n                    console.log(\"全局模板变化，开始更新学生模板:\", {\n                        newTemplate,\n                        personalTemplateAssignments: Array.from(currentPersonalAssignments.entries()),\n                        studentsCount: students.length\n                    });\n                    setStudents((prevStudents)=>prevStudents.map((student)=>{\n                            const hasPersonalTemplate = currentPersonalAssignments.has(student.userId);\n                            console.log(\"学生 \".concat(student.nickName, \" (\").concat(student.userId, \"):\"), {\n                                hasPersonalTemplate,\n                                currentTemplate: student.currentTemplate,\n                                willUpdate: !hasPersonalTemplate\n                            });\n                            // 如果学生有个人分配的模板，保持不变\n                            if (hasPersonalTemplate) {\n                                console.log(\"保持学生 \".concat(student.nickName, \" 的个人模板\"));\n                                return student;\n                            }\n                            // 否则更新为新的教师当前模板\n                            console.log(\"更新学生 \".concat(student.nickName, \" 为教师模板\"));\n                            return {\n                                ...student,\n                                currentTemplate: newTemplate\n                            };\n                        }));\n                }\n            });\n        }\n    }, [\n        globalTemplateChangeVersion,\n        classInfo === null || classInfo === void 0 ? void 0 : classInfo.id\n    ]);\n    // 定期检查教师模板更新\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!(classInfo === null || classInfo === void 0 ? void 0 : classInfo.id)) return;\n        // 立即检查一次\n        syncTeacherTemplateToStudents();\n        // 设置定时器，每30秒检查一次模板更新\n        const interval = setInterval(()=>{\n            syncTeacherTemplateToStudents();\n        }, 30000); // 30秒\n        return ()=>clearInterval(interval);\n    }, [\n        classInfo === null || classInfo === void 0 ? void 0 : classInfo.id,\n        students.length\n    ]);\n    // 点击外部关闭下拉菜单\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const handleClickOutside = (event)=>{\n            if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {\n            // setIsMoreActionsDropdownOpen(false); // 已移除\n            }\n            if (settingsDropdownRef.current && !settingsDropdownRef.current.contains(event.target)) {\n                setIsSettingsDropdownOpen(false);\n            }\n            if (batchActionsDropdownRef.current && !batchActionsDropdownRef.current.contains(event.target)) {\n                setIsBatchActionsDropdownOpen(false);\n            }\n        };\n        document.addEventListener(\"mousedown\", handleClickOutside);\n        return ()=>{\n            document.removeEventListener(\"mousedown\", handleClickOutside);\n        };\n    }, []);\n    // 处理学生点击选择（用于右侧显示详情）\n    const handleStudentClick = (student)=>{\n        setSelectedStudent(student);\n    };\n    // 处理删除学生\n    const handleDeleteStudent = async ()=>{\n        if (!selectedStudent) {\n            alert(\"请先选择要删除的学生\");\n            return;\n        }\n        // 显示确认对话框\n        const confirmed = window.confirm(\"确定要将 \".concat(selectedStudent.nickName, \" 移出班级吗？\\n\\n此操作不可恢复！\"));\n        if (!confirmed) {\n            return;\n        }\n        try {\n            console.log(\"删除学生:\", selectedStudent);\n            // 调用删除学生的API，传入学生的userId数组\n            const response = await _lib_api_class__WEBPACK_IMPORTED_MODULE_2__.classApi.removeStudentFromClass([\n                selectedStudent.userId\n            ]);\n            console.log(\"删除学生 API 响应:\", response.data);\n            // 检查响应状态\n            if (response.data.code === 200) {\n                console.log(\"删除学生成功\");\n                alert(\"学生已成功移出班级\");\n                // 清除选中的学生\n                setSelectedStudent(null);\n                // 重新获取学生列表\n                await fetchStudents();\n            } else {\n                console.error(\"删除学生失败:\", response.data.message);\n                alert(response.data.message || \"删除学生失败\");\n            }\n        } catch (error) {\n            console.error(\"删除学生失败:\", error);\n            alert(\"删除学生失败，请稍后重试\");\n        }\n    };\n    // 编辑班级\n    const handleEditClass = async (values)=>{\n        const { GetNotification } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! logic-common/dist/components/Notification */ \"(app-pages-browser)/./node_modules/logic-common/dist/components/Notification/index.js\"));\n        const notification = GetNotification();\n        try {\n            console.log(\"开始编辑班级:\", {\n                classId: classInfo.id,\n                values: values,\n                originalClassName: classInfo.className\n            });\n            const response = await _lib_api_class__WEBPACK_IMPORTED_MODULE_2__.classApi.updateClass(classInfo.id, {\n                className: values.className,\n                grade: classInfo.grade || \"\" // 确保传递grade字段，如果没有则使用空字符串\n            });\n            console.log(\"编辑班级API响应:\", response);\n            if (response.data.code === 200) {\n                console.log(\"编辑班级成功\");\n                notification.success(\"编辑班级成功\");\n                setIsEditClassModalVisible(false);\n                // 更新本地班级信息\n                const updatedClassInfo = {\n                    ...classInfo,\n                    className: values.className\n                };\n                setClassInfo(updatedClassInfo);\n                // 通知父组件更新班级列表中的班级信息\n                onClassInfoUpdate === null || onClassInfoUpdate === void 0 ? void 0 : onClassInfoUpdate(updatedClassInfo);\n            } else {\n                console.error(\"编辑班级失败 - API返回错误:\", {\n                    code: response.data.code,\n                    message: response.data.message,\n                    data: response.data\n                });\n                notification.error(response.data.message || \"编辑班级失败\");\n                throw new Error(response.data.message || \"编辑班级失败\"); // 抛出错误让模态框知道失败了\n            }\n        } catch (error) {\n            var _error_response, _error_response1;\n            console.error(\"编辑班级失败 - 请求异常:\", {\n                error: error,\n                message: error.message,\n                response: (_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.data,\n                status: (_error_response1 = error.response) === null || _error_response1 === void 0 ? void 0 : _error_response1.status\n            });\n            // 只有在没有显示过错误消息的情况下才显示通用错误\n            if (!error.message || error.message === \"编辑班级失败\") {\n                var _error_response_data, _error_response2;\n                notification.error(((_error_response2 = error.response) === null || _error_response2 === void 0 ? void 0 : (_error_response_data = _error_response2.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || \"编辑班级失败，请稍后重试\");\n            }\n            throw error; // 重新抛出错误，让模态框保持打开状态\n        }\n    };\n    // 导入学生\n    const handleImportStudents = async (file)=>{\n        try {\n            console.log(\"导入学生文件:\", file);\n            // 这里需要实现文件解析和导入逻辑\n            alert(\"导入学生功能正在开发中\");\n            setIsImportStudentModalVisible(false);\n            return true;\n        } catch (error) {\n            console.error(\"导入学生失败:\", error);\n            alert(\"导入学生失败，请稍后重试\");\n            return false;\n        }\n    };\n    // 导出学生\n    const handleExportStudents = async ()=>{\n        try {\n            const response = await _lib_api_student__WEBPACK_IMPORTED_MODULE_3__.studentApi.exportStudents(classInfo.id);\n            console.log(\"导出学生成功:\", response);\n            alert(\"导出学生成功\");\n        } catch (error) {\n            console.error(\"导出学生失败:\", error);\n            alert(\"导出学生失败，请稍后重试\");\n        }\n    };\n    // 搜索教师\n    const handleSearchTeacher = async (phone)=>{\n        try {\n            const response = await _lib_api_class__WEBPACK_IMPORTED_MODULE_2__.classApi.searchTeacherByPhone(phone);\n            console.log(\"搜索教师响应:\", response);\n            if (response.data.code === 200) {\n                setSearchedTeacher(response.data.data);\n            } else {\n                const { GetNotification } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! logic-common/dist/components/Notification */ \"(app-pages-browser)/./node_modules/logic-common/dist/components/Notification/index.js\"));\n                const notification = GetNotification();\n                notification.error(response.data.message || \"搜索教师失败\");\n            }\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error(\"搜索教师失败:\", error);\n            const { GetNotification } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! logic-common/dist/components/Notification */ \"(app-pages-browser)/./node_modules/logic-common/dist/components/Notification/index.js\"));\n            const notification = GetNotification();\n            notification.error(((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || \"搜索教师失败\");\n        }\n    };\n    // 转让班级\n    const handleTransferClass = async (values)=>{\n        const { GetNotification } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! logic-common/dist/components/Notification */ \"(app-pages-browser)/./node_modules/logic-common/dist/components/Notification/index.js\"));\n        const notification = GetNotification();\n        setTransferLoading(true);\n        try {\n            let newTeacherId;\n            if (values.transferType === \"search\") {\n                if (!searchedTeacher) {\n                    notification.error(\"请先搜索并选择教师\");\n                    return;\n                }\n                newTeacherId = searchedTeacher.id;\n            } else {\n                // 检查是否有协助教师\n                if (!classInfo.assistantTeacherId) {\n                    notification.error(\"该班级没有协助教师\");\n                    return;\n                }\n                newTeacherId = classInfo.assistantTeacherId;\n            }\n            const response = await _lib_api_class__WEBPACK_IMPORTED_MODULE_2__.classApi.transferClass(classInfo.id, newTeacherId, values.transferType);\n            if (response.data.code === 200) {\n                notification.success(\"转让班级成功\");\n                setIsTransferClassModalVisible(false);\n                setSearchedTeacher(null);\n                // 转让成功后返回班级管理页面\n                onBack();\n            } else {\n                notification.error(response.data.message || \"转让班级失败\");\n            }\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error(\"转让班级失败:\", error);\n            notification.error(((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || \"转让班级失败\");\n        } finally{\n            setTransferLoading(false);\n        }\n    };\n    // 移出协助教师\n    const handleRemoveAssistant = async ()=>{\n        const { GetNotification } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! logic-common/dist/components/Notification */ \"(app-pages-browser)/./node_modules/logic-common/dist/components/Notification/index.js\"));\n        const notification = GetNotification();\n        try {\n            // 这里需要调用移出协助教师的API\n            // 暂时使用转让API，将assistantTeacherId设为0\n            const response = await _lib_api_class__WEBPACK_IMPORTED_MODULE_2__.classApi.updateClass(classInfo.id, {\n                assistantTeacherId: 0\n            });\n            if (response.data.code === 200) {\n                notification.success(\"移出协助教师成功\");\n                setIsTransferClassModalVisible(false);\n                // 更新本地班级信息\n                const updatedClassInfo = {\n                    ...classInfo,\n                    assistantTeacherId: 0\n                };\n                setClassInfo(updatedClassInfo);\n                onClassInfoUpdate === null || onClassInfoUpdate === void 0 ? void 0 : onClassInfoUpdate(updatedClassInfo);\n            } else {\n                notification.error(response.data.message || \"移出协助教师失败\");\n            }\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error(\"移出协助教师失败:\", error);\n            notification.error(((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || \"移出协助教师失败\");\n        }\n    };\n    // 生成邀请码\n    const handleGenerateInviteCode = async ()=>{\n        try {\n            const response = await _lib_api_class__WEBPACK_IMPORTED_MODULE_2__.classApi.generateInviteCode(classInfo.id);\n            if (response.data.code === 200) {\n                setInviteCode(response.data.data.inviteCode);\n                setIsInviteCodeModalVisible(true);\n            } else {\n                console.error(\"生成邀请码失败:\", response.data.message);\n                alert(response.data.message || \"生成邀请码失败\");\n            }\n        } catch (error) {\n            console.error(\"生成邀请码失败:\", error);\n            alert(\"生成邀请码失败，请稍后重试\");\n        }\n    };\n    // 分配积木（批量或选中学生）\n    const handleAssignBlocks = async ()=>{\n        console.log(\"=== 分配积木开始 ===\");\n        console.log(\"selectedStudentIds:\", selectedStudentIds);\n        console.log(\"selectedStudentIds.length:\", selectedStudentIds.length);\n        // 如果有选中的学生，设置为单个学生分配模式\n        if (selectedStudentIds.length === 1) {\n            console.log(\"单个学生分配模式，studentId:\", selectedStudentIds[0]);\n            setSelectedStudentId(selectedStudentIds[0]);\n        } else {\n            console.log(\"批量分配模式，学生数量:\", selectedStudentIds.length);\n            setSelectedStudentId(null);\n        }\n        await fetchTemplates();\n        await fetchTemplateUsage();\n        setIsAssignBlocksModalVisible(true);\n    };\n    // 单独为学生分配积木\n    const handleIndividualAssignBlocks = async (studentId)=>{\n        console.log(\"=== 单独为学生分配积木 ===\");\n        console.log(\"studentId:\", studentId);\n        // 设置为单个学生分配模式\n        setSelectedStudentId(studentId);\n        setSelectedStudentIds([]); // 清空批量选择\n        await fetchTemplates();\n        await fetchTemplateUsage();\n        setIsAssignBlocksModalVisible(true);\n    };\n    // 删除班级\n    const handleDeleteClass = async ()=>{\n        const { Modal } = await __webpack_require__.e(/*! import() */ \"_app-pages-browser_node_modules_antd_es_index_js\").then(__webpack_require__.bind(__webpack_require__, /*! antd */ \"(app-pages-browser)/./node_modules/antd/es/index.js\"));\n        const { GetNotification } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! logic-common/dist/components/Notification */ \"(app-pages-browser)/./node_modules/logic-common/dist/components/Notification/index.js\"));\n        const notification = GetNotification();\n        // 先检查是否有学生\n        if (students.length > 0) {\n            Modal.warning({\n                title: \"无法删除班级\",\n                centered: true,\n                content: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            children: [\n                                \"班级中还有 \",\n                                students.length,\n                                \" 名学生，请先移除所有学生后再删除班级。\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                            lineNumber: 795,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-4 text-gray-500 text-sm\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: \"删除步骤：\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                    lineNumber: 797,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ol\", {\n                                    className: \"list-decimal ml-4 mt-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"选择要移除的学生\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                            lineNumber: 799,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"使用批量操作移除所有学生\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                            lineNumber: 800,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"再次尝试删除班级\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                            lineNumber: 801,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                    lineNumber: 798,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                            lineNumber: 796,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                    lineNumber: 794,\n                    columnNumber: 11\n                }, undefined),\n                okText: \"知道了\"\n            });\n            return;\n        }\n        // 如果没有学生，显示删除确认对话框\n        Modal.confirm({\n            title: \"确认删除班级\",\n            content: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: [\n                            \"您确定要删除 \",\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                children: classInfo.className\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                lineNumber: 816,\n                                columnNumber: 21\n                            }, undefined),\n                            \" 吗？\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                        lineNumber: 816,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-3 p-3 bg-red-50 rounded-lg border border-red-200\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2 text-red-600\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-4 h-4\",\n                                        fill: \"currentColor\",\n                                        viewBox: \"0 0 20 20\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            fillRule: \"evenodd\",\n                                            d: \"M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z\",\n                                            clipRule: \"evenodd\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                            lineNumber: 820,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                        lineNumber: 819,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-medium\",\n                                        children: \"此操作不可恢复！\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                        lineNumber: 822,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                lineNumber: 818,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"mt-1 text-sm text-red-500\",\n                                children: \"删除班级将永久移除班级信息，包括班级设置、模板配置等数据。\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                lineNumber: 824,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                        lineNumber: 817,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                lineNumber: 815,\n                columnNumber: 9\n            }, undefined),\n            okText: \"确定删除\",\n            cancelText: \"取消\",\n            okButtonProps: {\n                danger: true\n            },\n            centered: true,\n            onOk: async ()=>{\n                try {\n                    const response = await _lib_api_class__WEBPACK_IMPORTED_MODULE_2__.classApi.deleteClass(classInfo.id);\n                    if (response.data.code === 200) {\n                        notification.success(\"删除班级成功\");\n                        // 通知父组件班级已被删除\n                        onClassDeleted === null || onClassDeleted === void 0 ? void 0 : onClassDeleted(classInfo.id);\n                        // 返回到班级管理页面\n                        onBack();\n                    } else {\n                        notification.error(response.data.message || \"删除班级失败\");\n                    }\n                } catch (error) {\n                    var _error_response_data, _error_response;\n                    console.error(\"删除班级失败:\", error);\n                    if ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) {\n                        notification.error(error.response.data.message);\n                    } else {\n                        notification.error(\"删除班级失败，请稍后重试\");\n                    }\n                }\n            }\n        });\n    };\n    // 下拉菜单项处理函数\n    const handleMenuItemClick = (action)=>{\n        // setIsMoreActionsDropdownOpen(false); // 已移除\n        switch(action){\n            case \"edit\":\n                setIsEditClassModalVisible(true);\n                break;\n            case \"addStudent\":\n                setIsAddStudentModalVisible(true);\n                break;\n            case \"importStudent\":\n                setIsImportStudentModalVisible(true);\n                break;\n            case \"exportStudent\":\n                handleExportStudents();\n                break;\n            case \"transfer\":\n                setIsTransferClassModalVisible(true);\n                break;\n            case \"invite\":\n                handleGenerateInviteCode();\n                break;\n            case \"batchRedeem\":\n                console.log(\"批量兑换密令\");\n                setIsBatchUseKeyPackageModalVisible(true);\n                break;\n            case \"assignBlocks\":\n                handleAssignBlocks();\n                break;\n            case \"deleteClass\":\n                handleDeleteClass();\n                break;\n            default:\n                break;\n        }\n    };\n    // 设置下拉菜单项处理函数\n    const handleSettingsMenuItemClick = (action)=>{\n        setIsSettingsDropdownOpen(false);\n        switch(action){\n            case \"edit\":\n                setIsEditClassModalVisible(true);\n                break;\n            case \"addStudent\":\n                setIsAddStudentModalVisible(true);\n                break;\n            case \"importStudent\":\n                setIsImportStudentModalVisible(true);\n                break;\n            case \"exportStudent\":\n                handleExportStudents();\n                break;\n            case \"transfer\":\n                setIsTransferClassModalVisible(true);\n                break;\n            case \"invite\":\n                handleGenerateInviteCode();\n                break;\n            case \"batchRedeem\":\n                console.log(\"批量兑换密令\");\n                setIsBatchUseKeyPackageModalVisible(true);\n                break;\n            case \"assignBlocks\":\n                handleAssignBlocks();\n                break;\n            case \"deleteClass\":\n                handleDeleteClass();\n                break;\n            default:\n                break;\n        }\n    };\n    // 确保选中状态同步\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const shouldBeSelectAll = selectedStudentIds.length > 0 && selectedStudentIds.length === students.length;\n        if (isSelectAll !== shouldBeSelectAll) {\n            console.log(\"修复选中状态同步:\", {\n                isSelectAll,\n                shouldBeSelectAll,\n                selectedStudentIds: selectedStudentIds.length,\n                totalStudents: students.length\n            });\n            setIsSelectAll(shouldBeSelectAll);\n        }\n    }, [\n        selectedStudentIds,\n        students.length,\n        isSelectAll\n    ]);\n    // 全选/取消全选处理函数\n    const handleSelectAll = ()=>{\n        if (isSelectAll) {\n            // 取消全选\n            setSelectedStudentIds([]);\n            setIsSelectAll(false);\n        } else {\n            // 全选\n            const allStudentIds = students.map((student)=>student.userId);\n            setSelectedStudentIds(allStudentIds);\n            setIsSelectAll(true);\n        }\n    };\n    // 单个学生选择处理函数\n    const handleStudentSelect = (studentId)=>{\n        if (selectedStudentIds.includes(studentId)) {\n            // 取消选择\n            const newSelectedIds = selectedStudentIds.filter((id)=>id !== studentId);\n            setSelectedStudentIds(newSelectedIds);\n            setIsSelectAll(false);\n        } else {\n            // 选择\n            const newSelectedIds = [\n                ...selectedStudentIds,\n                studentId\n            ];\n            setSelectedStudentIds(newSelectedIds);\n            // 检查是否全选\n            if (newSelectedIds.length === students.length) {\n                setIsSelectAll(true);\n            }\n        }\n    };\n    // 批量操作处理函数\n    const handleBatchAction = (action)=>{\n        setIsBatchActionsDropdownOpen(false);\n        if (selectedStudentIds.length === 0) {\n            Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! logic-common/dist/components/Notification */ \"(app-pages-browser)/./node_modules/logic-common/dist/components/Notification/index.js\")).then((param)=>{\n                let { GetNotification } = param;\n                GetNotification().warning(\"请先选择要操作的学生\");\n            });\n            return;\n        }\n        switch(action){\n            case \"batchDelete\":\n                handleBatchRemoveStudents(selectedStudentIds);\n                break;\n            case \"batchAssignBlocks\":\n                handleAssignBlocks();\n                break;\n            case \"batchAssignPoints\":\n                // 清除单个学生选择，确保进入批量模式\n                setSelectedStudent(null);\n                setIsAssignPointsModalVisible(true);\n                break;\n            case \"batchUseKeyPackage\":\n                handleBatchUseKeyPackage();\n                break;\n            case \"batchExport\":\n                console.log(\"批量导出学生:\", selectedStudentIds);\n                Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! logic-common/dist/components/Notification */ \"(app-pages-browser)/./node_modules/logic-common/dist/components/Notification/index.js\")).then((param)=>{\n                    let { GetNotification } = param;\n                    GetNotification().info(\"批量导出学生功能正在开发中\");\n                });\n                break;\n            default:\n                break;\n        }\n    };\n    // 批量移出班级的改进版本\n    const handleBatchRemoveStudents = async (studentIds)=>{\n        try {\n            // 获取选中的学生信息\n            const selectedStudentsInfo = students.filter((s)=>studentIds.includes(s.userId));\n            // 计算总可用积分\n            const totalAvailablePoints = selectedStudentsInfo.reduce((sum, student)=>sum + (student.availablePoints || 0), 0);\n            const { Modal } = await __webpack_require__.e(/*! import() */ \"_app-pages-browser_node_modules_antd_es_index_js\").then(__webpack_require__.bind(__webpack_require__, /*! antd */ \"(app-pages-browser)/./node_modules/antd/es/index.js\"));\n            const { InfoCircleOutlined } = await __webpack_require__.e(/*! import() */ \"_app-pages-browser_node_modules_ant-design_icons_es_index_js\").then(__webpack_require__.bind(__webpack_require__, /*! @ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/index.js\"));\n            Modal.confirm({\n                title: \"确认批量移出班级\",\n                content: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            children: [\n                                \"确定要将选中的 \",\n                                studentIds.length,\n                                \" 名学生移出班级吗？\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                            lineNumber: 1037,\n                            columnNumber: 13\n                        }, undefined),\n                        totalAvailablePoints > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-2 p-3 bg-yellow-50 rounded-lg\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2 text-yellow-600\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(InfoCircleOutlined, {}, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                            lineNumber: 1041,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: [\n                                                \"选中的学生共有 \",\n                                                totalAvailablePoints,\n                                                \" 点可用能量\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                            lineNumber: 1042,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                    lineNumber: 1040,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"mt-1 text-sm text-yellow-500\",\n                                    children: \"移出班级后，可用能量将返还到各自的套餐积分中\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                    lineNumber: 1044,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                            lineNumber: 1039,\n                            columnNumber: 15\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                    lineNumber: 1036,\n                    columnNumber: 11\n                }, undefined),\n                okText: \"确定移出\",\n                cancelText: \"取消\",\n                centered: true,\n                okButtonProps: {\n                    danger: true\n                },\n                onOk: async ()=>{\n                    try {\n                        const { GetNotification } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! logic-common/dist/components/Notification */ \"(app-pages-browser)/./node_modules/logic-common/dist/components/Notification/index.js\"));\n                        const notification = GetNotification();\n                        const hideLoading = notification.loading(\"正在移出学生...\");\n                        const response = await _lib_api_class__WEBPACK_IMPORTED_MODULE_2__.classApi.removeStudentFromClass(studentIds);\n                        if (hideLoading) {\n                            hideLoading.close();\n                        }\n                        if (response.data.code === 200) {\n                            notification.success(\"成功移出 \".concat(studentIds.length, \" 名学生\"));\n                            // 清除选择状态\n                            setSelectedStudentIds([]);\n                            setIsSelectAll(false);\n                            setSelectedStudent(null);\n                            // 重新获取学生列表\n                            await fetchStudents();\n                        } else {\n                            notification.error(response.data.message || \"批量移出学生失败\");\n                        }\n                    } catch (error) {\n                        var _error_response_data, _error_response;\n                        const { GetNotification } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! logic-common/dist/components/Notification */ \"(app-pages-browser)/./node_modules/logic-common/dist/components/Notification/index.js\"));\n                        const notification = GetNotification();\n                        notification.error(((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || \"批量移出学生失败\");\n                        console.error(\"批量移出学生失败:\", error);\n                    }\n                }\n            });\n        } catch (error) {\n            console.error(\"批量移出学生失败:\", error);\n        }\n    };\n    // 处理添加学生\n    const handleAddStudent = async (values)=>{\n        try {\n            console.log(\"添加学生:\", values);\n            // 添加默认密码\n            const studentData = {\n                ...values,\n                password: \"123456\"\n            };\n            // 调用添加学生的API\n            const response = await _lib_api_class__WEBPACK_IMPORTED_MODULE_2__.classApi.addStudentToClass(classInfo.id, studentData);\n            console.log(\"添加学生 API 响应:\", response.data);\n            // 检查响应状态\n            if (response.data.code === 200) {\n                console.log(\"添加学生成功\");\n                setIsAddStudentModalVisible(false);\n                // 重新获取学生列表\n                await fetchStudents();\n            } else {\n                console.error(\"添加学生失败:\", response.data.message);\n                alert(response.data.message || \"添加学生失败\");\n            }\n        } catch (error) {\n            console.error(\"添加学生失败:\", error);\n            alert(\"添加学生失败，请稍后重试\");\n        }\n    };\n    // 处理文件上传\n    const handleUpload = async (file)=>{\n        setUploading(true);\n        try {\n            // 这里可以添加文件上传逻辑\n            console.log(\"上传文件:\", file);\n            return {\n                success: true\n            };\n        } catch (error) {\n            console.error(\"文件上传失败:\", error);\n            return {\n                success: false\n            };\n        } finally{\n            setUploading(false);\n        }\n    };\n    // 处理文件删除\n    const handleRemoveFile = async (file)=>{\n        try {\n            setFileList((prev)=>prev.filter((f)=>f.uid !== file.uid));\n            return true;\n        } catch (error) {\n            console.error(\"删除文件失败:\", error);\n            return false;\n        }\n    };\n    // 处理发布任务\n    const handlePublishTask = async (values)=>{\n        try {\n            console.log(\"发布任务:\", values);\n            // 导入taskApi\n            const { default: taskApi } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! ../../../lib/api/task */ \"(app-pages-browser)/./lib/api/task.ts\"));\n            const { GetNotification } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! logic-common/dist/components/Notification */ \"(app-pages-browser)/./node_modules/logic-common/dist/components/Notification/index.js\"));\n            const notification = GetNotification();\n            // 构建任务参数\n            const params = {\n                taskName: values.taskName,\n                taskDescription: values.taskDescription || \"\",\n                taskType: 1,\n                startDate: values.startDate ? new Date(values.startDate) : undefined,\n                endDate: values.endDate ? new Date(values.endDate) : new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),\n                taskContent: values.taskContent || \"\",\n                attachments: fileList.map((file)=>{\n                    var _file_response;\n                    return file.url || ((_file_response = file.response) === null || _file_response === void 0 ? void 0 : _file_response.url);\n                }).filter(Boolean),\n                teacherId: userId,\n                classId: classInfo === null || classInfo === void 0 ? void 0 : classInfo.id,\n                studentIds: selectedStudentIds.length > 0 ? selectedStudentIds : students.map((s)=>s.userId),\n                selfAssessmentItems: values.selfAssessmentItems || [],\n                priority: 1,\n                isPublic: 0,\n                allowLateSubmission: values.allowLateSubmission || false\n            };\n            console.log(\"发布任务参数:\", params);\n            // 调用发布任务API\n            const response = await taskApi.publishTask(params);\n            if (response.data.code === 200) {\n                // 显示详细的成功提示\n                const { Modal } = await __webpack_require__.e(/*! import() */ \"_app-pages-browser_node_modules_antd_es_index_js\").then(__webpack_require__.bind(__webpack_require__, /*! antd */ \"(app-pages-browser)/./node_modules/antd/es/index.js\"));\n                const studentCount = selectedStudentIds.length > 0 ? selectedStudentIds.length : students.length;\n                const className = (classInfo === null || classInfo === void 0 ? void 0 : classInfo.className) || \"当前班级\";\n                Modal.success({\n                    title: \"任务发布成功\",\n                    content: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: [\n                                    \"任务 \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: params.taskName\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                        lineNumber: 1194,\n                                        columnNumber: 21\n                                    }, undefined),\n                                    \" 已成功发布到 \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: className\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                        lineNumber: 1194,\n                                        columnNumber: 63\n                                    }, undefined),\n                                    \"。\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                lineNumber: 1194,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: [\n                                    \"共有 \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: studentCount\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                        lineNumber: 1195,\n                                        columnNumber: 21\n                                    }, undefined),\n                                    \" 名学生将收到此任务。\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                lineNumber: 1195,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"学生可以在班级空间查看和提交任务。\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                lineNumber: 1196,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                        lineNumber: 1193,\n                        columnNumber: 13\n                    }, undefined),\n                    okText: \"确定\",\n                    onOk: ()=>{\n                    // 可以在这里添加导航到任务管理页面的逻辑\n                    }\n                });\n                setIsPublishTaskModalVisible(false);\n                // 清理表单数据\n                setFileList([]);\n                setSelectedStudentIds([]);\n                setSelectedTemplateId(null);\n                // 刷新学生列表（如果需要显示任务相关信息）\n                await fetchStudents();\n            } else {\n                notification.error(response.data.message || \"任务发布失败\");\n            }\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error(\"发布任务失败:\", error);\n            const { GetNotification } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! logic-common/dist/components/Notification */ \"(app-pages-browser)/./node_modules/logic-common/dist/components/Notification/index.js\"));\n            const notification = GetNotification();\n            notification.error(((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || \"发布任务失败，请稍后重试\");\n        }\n    };\n    // 处理重置密码\n    const handleResetPassword = async ()=>{\n        try {\n            if (!selectedStudent) {\n                alert(\"请先选择要重置密码的学生\");\n                return;\n            }\n            console.log(\"重置密码:\", selectedStudent);\n            // 这里可以添加重置密码的API调用\n            setIsResetPasswordModalVisible(false);\n            alert(\"密码重置成功，新密码为：123456\");\n        } catch (error) {\n            console.error(\"重置密码失败:\", error);\n            alert(\"重置密码失败，请稍后重试\");\n        }\n    };\n    // 处理选择模板\n    const handleSelectTemplate = async (templateId)=>{\n        try {\n            const { addUserJoinRole, batchAddUserJoinRole } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! ../../../lib/api/role */ \"(app-pages-browser)/./lib/api/role.ts\"));\n            const { GetNotification } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! logic-common/dist/components/Notification */ \"(app-pages-browser)/./node_modules/logic-common/dist/components/Notification/index.js\"));\n            const notification = GetNotification();\n            // 确定要分配的学生\n            const targetStudents = selectedStudentId !== null ? [\n                selectedStudentId\n            ] : selectedStudentIds;\n            console.log(\"=== 模板分配详情 ===\");\n            console.log(\"selectedStudentId:\", selectedStudentId);\n            console.log(\"selectedStudentIds:\", selectedStudentIds);\n            console.log(\"targetStudents:\", targetStudents);\n            console.log(\"templateId:\", templateId);\n            if (targetStudents.length === 0) {\n                console.log(\"❌ 没有选中任何学生\");\n                notification.warning(\"请先选择学生\");\n                return;\n            }\n            const hideLoading = notification.loading(\"正在分配积木...\");\n            const userRolesMap = userRoles || [];\n            try {\n                // 准备用户数据 - 与teacher-space保持一致的逻辑\n                const usersData = targetStudents.map((userId)=>{\n                    const userInfo = userRolesMap.find((u)=>u.userId === userId);\n                    if (!(userInfo === null || userInfo === void 0 ? void 0 : userInfo.roleId)) return null;\n                    return {\n                        userId: userId,\n                        roleId: userInfo.roleId,\n                        templateId: templateId,\n                        originalTemplateId: templateId\n                    };\n                }).filter((item)=>item !== null);\n                console.log(\"准备分配模板:\", {\n                    templateId,\n                    targetStudents,\n                    userRolesMap,\n                    usersData\n                });\n                if (usersData.length === 0) {\n                    notification.error(\"无有效用户可分配\");\n                    return;\n                }\n                // 分批并发处理\n                const results = await (usersData.length > 20 ? Promise.all(Array.from({\n                    length: Math.ceil(usersData.length / 20)\n                }, (_, i)=>usersData.slice(i * 20, (i + 1) * 20)).map((batchUsers)=>batchAddUserJoinRole({\n                        users: batchUsers\n                    }).then((param)=>{\n                        let { data } = param;\n                        return data;\n                    }).catch(()=>({\n                            code: 500,\n                            data: {\n                                successCount: 0,\n                                failCount: batchUsers.length\n                            }\n                        })))).then((results)=>({\n                        code: results.some((r)=>r.code !== 200) ? 500 : 200,\n                        data: {\n                            successCount: results.reduce((sum, r)=>{\n                                var _r_data;\n                                return sum + (((_r_data = r.data) === null || _r_data === void 0 ? void 0 : _r_data.successCount) || 0);\n                            }, 0),\n                            failCount: results.reduce((sum, r)=>{\n                                var _r_data;\n                                return sum + (((_r_data = r.data) === null || _r_data === void 0 ? void 0 : _r_data.failCount) || 0);\n                            }, 0)\n                        }\n                    })) : batchAddUserJoinRole({\n                    users: usersData\n                }).then((param)=>{\n                    let { data } = param;\n                    return data;\n                }).catch(()=>({\n                        code: 500,\n                        data: {\n                            successCount: 0,\n                            failCount: usersData.length\n                        }\n                    })));\n                if (hideLoading) {\n                    hideLoading.close();\n                }\n                // 显示结果\n                if (results.code === 200) {\n                    const { successCount = 0, failCount = 0 } = results.data || {};\n                    if (successCount > 0 && failCount === 0) {\n                        notification.success(\"成功为 \".concat(successCount, \" 名学生分配积木\"));\n                    } else if (successCount > 0 && failCount > 0) {\n                        notification.warning(\"成功为 \".concat(successCount, \" 名学生分配积木，\").concat(failCount, \" 名学生分配失败\"));\n                    } else {\n                        notification.error(\"积木分配失败\");\n                    }\n                } else {\n                    notification.error(\"积木分配失败\");\n                }\n                // 立即更新已分配学生的模板信息，无需等待API刷新\n                const selectedTemplate = templates.find((t)=>t.id === templateId);\n                if (selectedTemplate) {\n                    const templateData = {\n                        templateId: templateId,\n                        templateName: selectedTemplate.templateName || selectedTemplate.name,\n                        isOfficial: selectedTemplate.isOfficial || false\n                    };\n                    // 更新personalTemplateAssignments Map，确保数据持久化\n                    setPersonalTemplateAssignments((prev)=>{\n                        const newMap = new Map(prev);\n                        targetStudents.forEach((studentId)=>{\n                            newMap.set(studentId, templateData);\n                        });\n                        return newMap;\n                    });\n                    // 更新学生状态\n                    const updatedStudents = students.map((student)=>{\n                        if (targetStudents.includes(student.userId)) {\n                            // 被选中的学生：设置为新分配的模板\n                            return {\n                                ...student,\n                                currentTemplate: templateData\n                            };\n                        }\n                        // 未选中的学生：保持原有状态\n                        return student;\n                    });\n                    setStudents(updatedStudents);\n                    console.log(\"模板分配成功，已更新personalTemplateAssignments:\", {\n                        targetStudents,\n                        templateData,\n                        personalTemplateAssignmentsBefore: Array.from(personalTemplateAssignments.entries()),\n                        personalTemplateAssignmentsAfter: \"will be updated\"\n                    });\n                    // 延迟打印更新后的状态\n                    setTimeout(()=>{\n                        console.log(\"personalTemplateAssignments更新后:\", Array.from(personalTemplateAssignments.entries()));\n                    }, 100);\n                }\n                // 关闭弹窗并清理状态\n                setIsAssignBlocksModalVisible(false);\n                setSelectedStudentId(null);\n                setSelectedStudentIds([]); // 清空选中的学生\n                setIsSelectAll(false); // 取消全选状态\n                // 刷新相关数据\n                await fetchTemplateUsage(); // 刷新模板使用情况\n            // 当前模板信息由全局状态管理，无需手动刷新\n            } catch (error) {\n                if (hideLoading) {\n                    hideLoading.close();\n                }\n                throw error;\n            }\n        } catch (error) {\n            console.error(\"分配模板失败:\", error);\n            const { GetNotification } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! logic-common/dist/components/Notification */ \"(app-pages-browser)/./node_modules/logic-common/dist/components/Notification/index.js\"));\n            const notification = GetNotification();\n            notification.error(\"分配模板失败，请稍后重试\");\n        }\n    };\n    // 处理模板使用情况点击\n    const handleTemplateUsageClick = (e, template)=>{\n        e.stopPropagation();\n        console.log(\"查看模板使用情况:\", template);\n    };\n    // 处理单个学生分配能量\n    const handleAssignPoints = async (values)=>{\n        var _values_studentExpiries;\n        if (!selectedStudent) {\n            const { GetNotification } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! logic-common/dist/components/Notification */ \"(app-pages-browser)/./node_modules/logic-common/dist/components/Notification/index.js\"));\n            const notification = GetNotification();\n            notification.error(\"请先选择学生\");\n            return;\n        }\n        // 从 studentExpiries 中提取单个学生的过期时间\n        const expireTime = (_values_studentExpiries = values.studentExpiries) === null || _values_studentExpiries === void 0 ? void 0 : _values_studentExpiries[selectedStudent.userId];\n        try {\n            const { GetNotification } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! logic-common/dist/components/Notification */ \"(app-pages-browser)/./node_modules/logic-common/dist/components/Notification/index.js\"));\n            const notification = GetNotification();\n            const hideLoading = notification.loading(\"正在分配能量...\");\n            const { pointsApi } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! ../../../lib/api/points */ \"(app-pages-browser)/./lib/api/points.ts\"));\n            await pointsApi.assignPermission({\n                studentUserId: selectedStudent.userId,\n                availablePoints: values.availablePoints,\n                expireTime: expireTime,\n                remark: values.remark\n            });\n            if (hideLoading) {\n                hideLoading.close();\n            }\n            notification.success(\"分配能量成功\");\n            setIsAssignPointsModalVisible(false);\n            // 刷新学生列表\n            await refreshStudentList();\n        } catch (error) {\n            var _error_response_data, _error_response;\n            const { GetNotification } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! logic-common/dist/components/Notification */ \"(app-pages-browser)/./node_modules/logic-common/dist/components/Notification/index.js\"));\n            const notification = GetNotification();\n            // 增加更具体的错误提示\n            notification.error(((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || \"分配能量失败\");\n        }\n    };\n    // 刷新学生列表\n    const refreshStudentList = async ()=>{\n        await fetchStudents();\n    };\n    // 更新教师的当前模板（通过UserJoinRole表）\n    const updateClassCurrentTemplate = async (templateId, templateName, isOfficial)=>{\n        try {\n            console.log(\"更新教师当前模板:\", {\n                userId,\n                roleId,\n                templateId,\n                templateName,\n                isOfficial\n            });\n            // 使用addUserJoinRole API来更新教师的模板\n            const { addUserJoinRole } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! ../../../lib/api/role */ \"(app-pages-browser)/./lib/api/role.ts\"));\n            const response = await addUserJoinRole({\n                userId: userId,\n                roleId: roleId || 2,\n                templateId: templateId\n            });\n            if (response.data.code === 200) {\n                // 更新本地的全局当前模板状态\n                const newTemplate = {\n                    templateId: templateId,\n                    templateName: templateName,\n                    isOfficial: isOfficial\n                };\n                setGlobalCurrentTemplate(newTemplate);\n                console.log(\"教师当前模板更新成功:\", newTemplate);\n            } else {\n                console.error(\"更新教师当前模板失败:\", response.data);\n            }\n        } catch (error) {\n            console.error(\"更新教师当前模板失败:\", error);\n        }\n    };\n    // 更新学生的当前模板信息\n    const handleUpdateStudentTemplate = (studentIds, templateInfo)=>{\n        const templateData = {\n            templateId: templateInfo.templateId,\n            templateName: templateInfo.templateName,\n            isOfficial: templateInfo.isOfficial || false\n        };\n        // 保存个人分配的模板信息\n        setPersonalTemplateAssignments((prev)=>{\n            const newMap = new Map(prev);\n            studentIds.forEach((studentId)=>{\n                newMap.set(studentId, templateData);\n            });\n            return newMap;\n        });\n        // 更新学生状态\n        setStudents((prevStudents)=>prevStudents.map((student)=>{\n                if (studentIds.includes(student.userId)) {\n                    return {\n                        ...student,\n                        currentTemplate: templateData\n                    };\n                }\n                return student;\n            }));\n    // 不再自动更新教师模板，只分配给学生\n    // updateClassCurrentTemplate(templateData.templateId, templateData.templateName, templateData.isOfficial);\n    };\n    // 处理批量/单个兑换密钥\n    const handleBatchUseKeyPackage = (studentId)=>{\n        if (studentId) {\n            // 单个学生触发\n            setSelectedStudentIds([\n                studentId\n            ]);\n            setIsBatchUseKeyPackageModalVisible(true);\n        } else {\n            // 批量操作触发\n            if (selectedStudentIds.length === 0) {\n                Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! logic-common/dist/components/Notification */ \"(app-pages-browser)/./node_modules/logic-common/dist/components/Notification/index.js\")).then((param)=>{\n                    let { GetNotification } = param;\n                    GetNotification().warning(\"请先选择学生\");\n                });\n                return;\n            }\n            setIsBatchUseKeyPackageModalVisible(true);\n        }\n    };\n    // 处理兑换密令成功\n    const handleBatchUseKeyPackageSuccess = ()=>{\n        refreshStudentList();\n        setIsBatchUseKeyPackageModalVisible(false);\n    };\n    // 处理从兑换密令跳转到分配能量\n    const handleGoToAssignPointsFromRedeem = (studentIds)=>{\n        setIsBatchUseKeyPackageModalVisible(false);\n        // 设置选中的学生\n        setSelectedStudentIds(studentIds);\n        // 打开分配能量弹窗\n        setIsAssignPointsModalVisible(true);\n    };\n    // 批量分配能量处理函数\n    const handleBatchAssignPoints = async (values)=>{\n        if (selectedStudentIds.length === 0) {\n            const { GetNotification } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! logic-common/dist/components/Notification */ \"(app-pages-browser)/./node_modules/logic-common/dist/components/Notification/index.js\"));\n            const notification = GetNotification();\n            notification.error(\"请先选择学生\");\n            return;\n        }\n        if (!values.studentExpiries) {\n            const { GetNotification } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! logic-common/dist/components/Notification */ \"(app-pages-browser)/./node_modules/logic-common/dist/components/Notification/index.js\"));\n            const notification = GetNotification();\n            notification.error(\"未能获取学生过期时间信息\");\n            return;\n        }\n        try {\n            const { GetNotification } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! logic-common/dist/components/Notification */ \"(app-pages-browser)/./node_modules/logic-common/dist/components/Notification/index.js\"));\n            const notification = GetNotification();\n            const hideLoading = notification.loading(\"正在批量分配能量...\");\n            // 调用新的批量分配 API\n            const { pointsApi } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! ../../../lib/api/points */ \"(app-pages-browser)/./lib/api/points.ts\"));\n            const response = await pointsApi.batchAssignPermission({\n                availablePoints: values.availablePoints,\n                studentExpiries: values.studentExpiries,\n                remark: values.remark\n            });\n            console.log(\"批量分配积分res\", response);\n            if (hideLoading) {\n                hideLoading.close();\n            }\n            if (response.data.code === 200) {\n                // 后端现在返回处理结果数组，可以根据需要处理\n                const results = response.data.data;\n                // 可以根据 results 中的信息给出更详细的成功/失败提示，\n                // 但为了简单起见，我们仍然使用之前的逻辑\n                notification.success(\"成功为 \".concat(results.success, \" 名学生分配能量\"));\n                setIsAssignPointsModalVisible(false);\n                // 刷新学生列表\n                await refreshStudentList(); // 确保使用 await\n                setSelectedStudentIds([]); // 清空选择\n                setIsSelectAll(false);\n            } else {\n                // 处理 API 返回的错误信息\n                notification.error(response.data.message || \"批量分配能量失败\");\n            }\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error(\"批量分配能量失败:\", error);\n            const { GetNotification } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! logic-common/dist/components/Notification */ \"(app-pages-browser)/./node_modules/logic-common/dist/components/Notification/index.js\"));\n            const notification = GetNotification();\n            // 处理请求级别的错误\n            notification.error(((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || \"批量分配能量失败，请检查网络连接或稍后重试\");\n        }\n    };\n    if (!selectedSchool) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"class-detail-container\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"class-detail-content\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"error-message\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            children: \"学校信息不存在\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                            lineNumber: 1624,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: onBack,\n                            className: \"back-button\",\n                            children: \"返回班级管理\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                            lineNumber: 1625,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                    lineNumber: 1623,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                lineNumber: 1622,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n            lineNumber: 1621,\n            columnNumber: 7\n        }, undefined);\n    }\n    // 生成随机头像颜色\n    const getAvatarColor = (index)=>{\n        const colors = [\n            \"linear-gradient(135deg, #667eea 0%, #764ba2 100%)\",\n            \"linear-gradient(135deg, #f093fb 0%, #f5576c 100%)\",\n            \"linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)\",\n            \"linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)\",\n            \"linear-gradient(135deg, #fa709a 0%, #fee140 100%)\",\n            \"linear-gradient(135deg, #a8edea 0%, #fed6e3 100%)\"\n        ];\n        // 确保index是有效的正数\n        const safeIndex = Math.max(0, index);\n        return colors[safeIndex % colors.length];\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"class-detail-container\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"class-detail-header\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    className: \"back-button\",\n                    onClick: onBack,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Blocks_ChevronDown_Download_Edit_Gift_Link_Plus_Search_Settings_Trash2_Upload_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                            size: 20\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                            lineNumber: 1657,\n                            columnNumber: 11\n                        }, undefined),\n                        \"返回班级管理\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                    lineNumber: 1653,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                lineNumber: 1652,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"class-detail-content\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"left-section\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"left-section-header\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"settings-container\",\n                                    ref: settingsDropdownRef,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: \"settings-btn \".concat(isSettingsDropdownOpen ? \"active\" : \"\"),\n                                            onClick: ()=>setIsSettingsDropdownOpen(!isSettingsDropdownOpen),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Blocks_ChevronDown_Download_Edit_Gift_Link_Plus_Search_Settings_Trash2_Upload_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                size: 20\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                lineNumber: 1672,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                            lineNumber: 1668,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        isSettingsDropdownOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"dropdown-menu\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"dropdown-menu-items\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"dropdown-menu-item\",\n                                                        onClick: ()=>handleSettingsMenuItemClick(\"edit\"),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"dropdown-menu-item-icon\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Blocks_ChevronDown_Download_Edit_Gift_Link_Plus_Search_Settings_Trash2_Upload_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                    size: 16,\n                                                                    style: {\n                                                                        color: \"#8b5cf6\"\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                    lineNumber: 1683,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                lineNumber: 1682,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"编辑班级\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                lineNumber: 1685,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                        lineNumber: 1678,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"dropdown-menu-item\",\n                                                        onClick: ()=>handleSettingsMenuItemClick(\"addStudent\"),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"dropdown-menu-item-icon\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Blocks_ChevronDown_Download_Edit_Gift_Link_Plus_Search_Settings_Trash2_Upload_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                    size: 16,\n                                                                    style: {\n                                                                        color: \"#10b981\"\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                    lineNumber: 1693,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                lineNumber: 1692,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"添加学生\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                lineNumber: 1695,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                        lineNumber: 1688,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"dropdown-menu-item\",\n                                                        onClick: ()=>handleSettingsMenuItemClick(\"importStudent\"),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"dropdown-menu-item-icon\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Blocks_ChevronDown_Download_Edit_Gift_Link_Plus_Search_Settings_Trash2_Upload_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                    size: 16,\n                                                                    style: {\n                                                                        color: \"#3b82f6\"\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                    lineNumber: 1703,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                lineNumber: 1702,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"导入学生\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                lineNumber: 1705,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                        lineNumber: 1698,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"dropdown-menu-item\",\n                                                        onClick: ()=>handleSettingsMenuItemClick(\"exportStudent\"),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"dropdown-menu-item-icon\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Blocks_ChevronDown_Download_Edit_Gift_Link_Plus_Search_Settings_Trash2_Upload_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                    size: 16,\n                                                                    style: {\n                                                                        color: \"#10b981\"\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                    lineNumber: 1713,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                lineNumber: 1712,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"导出学生\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                lineNumber: 1715,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                        lineNumber: 1708,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"dropdown-menu-item\",\n                                                        onClick: ()=>handleSettingsMenuItemClick(\"transfer\"),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"dropdown-menu-item-icon\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Blocks_ChevronDown_Download_Edit_Gift_Link_Plus_Search_Settings_Trash2_Upload_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                    size: 16,\n                                                                    style: {\n                                                                        color: \"#f59e0b\"\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                    lineNumber: 1723,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                lineNumber: 1722,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"转让管理\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                lineNumber: 1725,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                        lineNumber: 1718,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"dropdown-menu-item\",\n                                                        onClick: ()=>handleSettingsMenuItemClick(\"invite\"),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"dropdown-menu-item-icon\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Blocks_ChevronDown_Download_Edit_Gift_Link_Plus_Search_Settings_Trash2_Upload_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                    size: 16,\n                                                                    style: {\n                                                                        color: \"#3b82f6\"\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                    lineNumber: 1733,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                lineNumber: 1732,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"生成邀请码\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                lineNumber: 1735,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                        lineNumber: 1728,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"dropdown-menu-item\",\n                                                        onClick: ()=>handleSettingsMenuItemClick(\"batchRedeem\"),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"dropdown-menu-item-icon\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Blocks_ChevronDown_Download_Edit_Gift_Link_Plus_Search_Settings_Trash2_Upload_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                    size: 16,\n                                                                    style: {\n                                                                        color: \"#f59e0b\"\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                    lineNumber: 1743,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                lineNumber: 1742,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"批量兑换密令\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                lineNumber: 1745,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                        lineNumber: 1738,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"dropdown-menu-item\",\n                                                        onClick: ()=>handleSettingsMenuItemClick(\"assignBlocks\"),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"dropdown-menu-item-icon\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Blocks_ChevronDown_Download_Edit_Gift_Link_Plus_Search_Settings_Trash2_Upload_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                    size: 16,\n                                                                    style: {\n                                                                        color: \"#3b82f6\"\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                    lineNumber: 1753,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                lineNumber: 1752,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"分配积木\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                lineNumber: 1755,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                        lineNumber: 1748,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"dropdown-menu-divider\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                        lineNumber: 1758,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"dropdown-menu-item danger\",\n                                                        onClick: ()=>handleSettingsMenuItemClick(\"deleteClass\"),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"dropdown-menu-item-icon\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Blocks_ChevronDown_Download_Edit_Gift_Link_Plus_Search_Settings_Trash2_Upload_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                    size: 16\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                    lineNumber: 1765,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                lineNumber: 1764,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"删除班级\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                lineNumber: 1767,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                        lineNumber: 1760,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                lineNumber: 1677,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                            lineNumber: 1676,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                    lineNumber: 1667,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                lineNumber: 1666,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"class-info-header\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"class-avatar\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"robot-avatar\",\n                                            children: \"\\uD83E\\uDD16\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                            lineNumber: 1778,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                        lineNumber: 1777,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"class-info\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"school-name\",\n                                                children: selectedSchool.schoolName\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                lineNumber: 1781,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"class-name\",\n                                                children: classInfo.className\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                lineNumber: 1782,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                        lineNumber: 1780,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                lineNumber: 1776,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"search-section\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"search-box\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Blocks_ChevronDown_Download_Edit_Gift_Link_Plus_Search_Settings_Trash2_Upload_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                        size: 16,\n                                        className: \"search-icon\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                        lineNumber: 1789,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                    lineNumber: 1788,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                lineNumber: 1787,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"students-section\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"students-header\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                children: \"学生\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                lineNumber: 1796,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"students-actions\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        className: \"add-student-btn\",\n                                                        onClick: ()=>setIsAddStudentModalVisible(true),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Blocks_ChevronDown_Download_Edit_Gift_Link_Plus_Search_Settings_Trash2_Upload_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                            size: 16\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                            lineNumber: 1802,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                        lineNumber: 1798,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        className: \"select-all-btn \".concat(isSelectAll ? \"active\" : \"\"),\n                                                        onClick: ()=>{\n                                                            console.log(\"点击全选按钮，当前状态:\", {\n                                                                isSelectAll,\n                                                                selectedStudentIds: selectedStudentIds.length,\n                                                                totalStudents: students.length\n                                                            });\n                                                            handleSelectAll();\n                                                        },\n                                                        title: isSelectAll ? \"取消全选\" : \"全选\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: isSelectAll ? \"取消全选\" : \"全选\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                            lineNumber: 1816,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                        lineNumber: 1804,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    selectedStudentIds.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"batch-actions-container\",\n                                                        ref: batchActionsDropdownRef,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                className: \"batch-actions-btn\",\n                                                                onClick: ()=>setIsBatchActionsDropdownOpen(!isBatchActionsDropdownOpen),\n                                                                title: \"批量操作\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: [\n                                                                            \"批量操作(\",\n                                                                            selectedStudentIds.length,\n                                                                            \")\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                        lineNumber: 1827,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Blocks_ChevronDown_Download_Edit_Gift_Link_Plus_Search_Settings_Trash2_Upload_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                                        size: 16\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                        lineNumber: 1828,\n                                                                        columnNumber: 23\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                lineNumber: 1822,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            isBatchActionsDropdownOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"batch-actions-dropdown\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        className: \"batch-action-item\",\n                                                                        onClick: ()=>handleBatchAction(\"batchAssignBlocks\"),\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Blocks_ChevronDown_Download_Edit_Gift_Link_Plus_Search_Settings_Trash2_Upload_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                                size: 16\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                                lineNumber: 1838,\n                                                                                columnNumber: 27\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                children: \"批量分配积木\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                                lineNumber: 1839,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                        lineNumber: 1834,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        className: \"batch-action-item\",\n                                                                        onClick: ()=>handleBatchAction(\"batchAssignPoints\"),\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Blocks_ChevronDown_Download_Edit_Gift_Link_Plus_Search_Settings_Trash2_Upload_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                                                                size: 16\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                                lineNumber: 1845,\n                                                                                columnNumber: 27\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                children: \"批量分配能量\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                                lineNumber: 1846,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                        lineNumber: 1841,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        className: \"batch-action-item\",\n                                                                        onClick: ()=>handleBatchAction(\"batchUseKeyPackage\"),\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Blocks_ChevronDown_Download_Edit_Gift_Link_Plus_Search_Settings_Trash2_Upload_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                                size: 16\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                                lineNumber: 1852,\n                                                                                columnNumber: 27\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                children: \"批量兑换密令\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                                lineNumber: 1853,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                        lineNumber: 1848,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        className: \"batch-action-item\",\n                                                                        onClick: ()=>handleBatchAction(\"batchDelete\"),\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Blocks_ChevronDown_Download_Edit_Gift_Link_Plus_Search_Settings_Trash2_Upload_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                                size: 16\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                                lineNumber: 1859,\n                                                                                columnNumber: 27\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                children: \"批量移出班级\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                                lineNumber: 1860,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                        lineNumber: 1855,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        className: \"batch-action-item\",\n                                                                        onClick: ()=>handleBatchAction(\"batchExport\"),\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Blocks_ChevronDown_Download_Edit_Gift_Link_Plus_Search_Settings_Trash2_Upload_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                                size: 16\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                                lineNumber: 1866,\n                                                                                columnNumber: 27\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                children: \"批量导出\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                                lineNumber: 1867,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                        lineNumber: 1862,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                lineNumber: 1833,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                        lineNumber: 1821,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                lineNumber: 1797,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                        lineNumber: 1795,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"students-list\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_StudentList__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            students: students,\n                                            loading: loading,\n                                            error: error,\n                                            selectedStudent: selectedStudent,\n                                            selectedStudentIds: selectedStudentIds,\n                                            currentTemplate: globalCurrentTemplate || currentTemplate,\n                                            renderVersion: renderVersion,\n                                            onStudentClick: handleStudentClick,\n                                            onStudentSelect: handleStudentSelect,\n                                            onRetry: fetchStudents,\n                                            onIndividualAssignBlocks: handleIndividualAssignBlocks,\n                                            onAssignPoints: (studentId)=>{\n                                                const student = students.find((s)=>s.userId === studentId);\n                                                if (student) {\n                                                    setSelectedStudent(student);\n                                                    setIsAssignPointsModalVisible(true);\n                                                }\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                            lineNumber: 1877,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                        lineNumber: 1876,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                lineNumber: 1794,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                        lineNumber: 1664,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"right-section\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"student-info-header\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"student-avatar-large\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"student-avatar-circle\",\n                                            style: {\n                                                background: selectedStudent ? getAvatarColor(Math.max(0, students.findIndex((s)=>s.userId === selectedStudent.userId))) : getAvatarColor(0)\n                                            },\n                                            children: selectedStudent ? ((_selectedStudent_nickName = selectedStudent.nickName) === null || _selectedStudent_nickName === void 0 ? void 0 : _selectedStudent_nickName.charAt(0)) || \"S\" : \"S\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                            lineNumber: 1906,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                        lineNumber: 1905,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"student-details\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"student-name-large\",\n                                                children: selectedStudent ? selectedStudent.nickName || \"学生\".concat(selectedStudent.studentNumber || selectedStudent.userId) : \"请选择学生\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                lineNumber: 1918,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"student-id-large\",\n                                                children: selectedStudent ? selectedStudent.studentNumber || \"无学号\" : \"\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                lineNumber: 1921,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                        lineNumber: 1917,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"delete-student-btn\",\n                                        onClick: handleDeleteStudent,\n                                        disabled: !selectedStudent,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Blocks_ChevronDown_Download_Edit_Gift_Link_Plus_Search_Settings_Trash2_Upload_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                            size: 20\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                            lineNumber: 1930,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                        lineNumber: 1925,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                lineNumber: 1904,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"functions-container\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"functions-section\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                children: \"更多功能\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                lineNumber: 1938,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"function-buttons\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        className: \"function-btn publish-task\",\n                                                        onClick: ()=>setIsPublishTaskModalVisible(true),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"function-icon\",\n                                                                children: \"\\uD83D\\uDCDD\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                lineNumber: 1944,\n                                                                columnNumber: 19\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"发布任务\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                lineNumber: 1945,\n                                                                columnNumber: 19\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                        lineNumber: 1940,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        className: \"function-btn distribute-blocks\",\n                                                        onClick: handleAssignBlocks,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"function-icon\",\n                                                                children: \"\\uD83E\\uDDE9\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                lineNumber: 1951,\n                                                                columnNumber: 19\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"分配积木\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                lineNumber: 1952,\n                                                                columnNumber: 19\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                        lineNumber: 1947,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        className: \"function-btn distribute-energy\",\n                                                        onClick: ()=>setIsAssignPointsModalVisible(true),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"function-icon\",\n                                                                children: \"⚡\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                lineNumber: 1958,\n                                                                columnNumber: 19\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"分配能量\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                lineNumber: 1959,\n                                                                columnNumber: 19\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                        lineNumber: 1954,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        className: \"function-btn exchange-tokens\",\n                                                        onClick: ()=>setIsBatchUseKeyPackageModalVisible(true),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"function-icon\",\n                                                                children: \"\\uD83C\\uDF81\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                lineNumber: 1965,\n                                                                columnNumber: 19\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"兑换密令\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                lineNumber: 1966,\n                                                                columnNumber: 19\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                        lineNumber: 1961,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        className: \"function-btn reset-password\",\n                                                        onClick: ()=>setIsResetPasswordModalVisible(true),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"function-icon\",\n                                                                children: \"\\uD83D\\uDD11\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                lineNumber: 1972,\n                                                                columnNumber: 19\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"重置密码\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                lineNumber: 1973,\n                                                                columnNumber: 19\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                        lineNumber: 1968,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                lineNumber: 1939,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                        lineNumber: 1937,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"learning-status\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                children: \"课程学习情况\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                lineNumber: 1980,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"status-placeholder\",\n                                                children: \"该区域功能正在等待开放\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                lineNumber: 1981,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                        lineNumber: 1979,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                lineNumber: 1935,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"energy-section\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"energy-header\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"energy-label\",\n                                                children: \"可用能量/总能量\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                lineNumber: 1990,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"energy-display\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"energy-available-number\",\n                                                        children: (selectedStudent === null || selectedStudent === void 0 ? void 0 : selectedStudent.availablePoints) || 0\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                        lineNumber: 1992,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"energy-total-number\",\n                                                        children: [\n                                                            \"/\",\n                                                            (selectedStudent === null || selectedStudent === void 0 ? void 0 : selectedStudent.totalPoints) || 0\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                        lineNumber: 1995,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                lineNumber: 1991,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                        lineNumber: 1989,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"energy-bar\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"energy-progress\",\n                                            style: {\n                                                width: (selectedStudent === null || selectedStudent === void 0 ? void 0 : selectedStudent.totalPoints) ? \"\".concat(Number(selectedStudent.availablePoints || 0) / Number(selectedStudent.totalPoints || 0) * 100, \"%\") : \"0%\"\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                            lineNumber: 2001,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                        lineNumber: 2000,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                lineNumber: 1988,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"template-section\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"template-header\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"当前模板\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                lineNumber: 2015,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            (()=>{\n                                                const displayTemplate = (selectedStudent === null || selectedStudent === void 0 ? void 0 : selectedStudent.currentTemplate) || globalCurrentTemplate || currentTemplate;\n                                                return displayTemplate && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"template-badge \".concat(displayTemplate.isOfficial ? \"\" : \"custom\"),\n                                                    children: displayTemplate.isOfficial ? \"官方\" : \"自定义\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                    lineNumber: 2019,\n                                                    columnNumber: 19\n                                                }, undefined);\n                                            })()\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                        lineNumber: 2014,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"template-info\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"template-icon\",\n                                                children: \"\\uD83E\\uDDE9\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                lineNumber: 2026,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: !(selectedStudent === null || selectedStudent === void 0 ? void 0 : selectedStudent.currentTemplate) && !globalCurrentTemplate && !currentTemplate ? \"template-loading\" : \"\",\n                                                children: (()=>{\n                                                    if (selectedStudent === null || selectedStudent === void 0 ? void 0 : selectedStudent.currentTemplate) {\n                                                        return selectedStudent.currentTemplate.templateName;\n                                                    } else if (globalCurrentTemplate) {\n                                                        return globalCurrentTemplate.templateName;\n                                                    } else if (currentTemplate) {\n                                                        return currentTemplate.templateName;\n                                                    } else if (selectedStudent) {\n                                                        return \"加载中...\";\n                                                    } else {\n                                                        return \"请选择学生\";\n                                                    }\n                                                })()\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                lineNumber: 2027,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            !(selectedStudent === null || selectedStudent === void 0 ? void 0 : selectedStudent.currentTemplate) && !globalCurrentTemplate && !currentTemplate && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: async ()=>{\n                                                    await fetchTeacherCurrentTemplate();\n                                                    await refreshCurrentTemplate();\n                                                },\n                                                style: {\n                                                    marginLeft: \"8px\",\n                                                    padding: \"2px 6px\",\n                                                    fontSize: \"10px\",\n                                                    backgroundColor: \"#2196F3\",\n                                                    color: \"white\",\n                                                    border: \"none\",\n                                                    borderRadius: \"3px\",\n                                                    cursor: \"pointer\"\n                                                },\n                                                children: \"刷新\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                lineNumber: 2044,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                        lineNumber: 2025,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                lineNumber: 2013,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                        lineNumber: 1902,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                lineNumber: 1662,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AddStudentModal__WEBPACK_IMPORTED_MODULE_5__.AddStudentModal, {\n                visible: isAddStudentModalVisible,\n                onCancel: ()=>setIsAddStudentModalVisible(false),\n                onOk: handleAddStudent\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                lineNumber: 2069,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_teacher_space_components_modals__WEBPACK_IMPORTED_MODULE_6__.EditClassModal, {\n                visible: isEditClassModalVisible,\n                onCancel: ()=>setIsEditClassModalVisible(false),\n                onOk: handleEditClass,\n                initialValues: {\n                    className: classInfo.className\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                lineNumber: 2076,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_teacher_space_components_modals__WEBPACK_IMPORTED_MODULE_6__.ImportStudentModal, {\n                visible: isImportStudentModalVisible,\n                onCancel: ()=>setIsImportStudentModalVisible(false),\n                onImport: handleImportStudents,\n                classId: classInfo.id\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                lineNumber: 2086,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_TransferClassModal__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                visible: isTransferClassModalVisible,\n                onCancel: ()=>{\n                    setIsTransferClassModalVisible(false);\n                    setSearchedTeacher(null);\n                },\n                onOk: handleTransferClass,\n                onSearchTeacher: handleSearchTeacher,\n                searchedTeacher: searchedTeacher,\n                hasAssistantTeacher: !!classInfo.assistantTeacherId,\n                onRemoveAssistant: handleRemoveAssistant,\n                loading: transferLoading\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                lineNumber: 2094,\n                columnNumber: 7\n            }, undefined),\n            isInviteCodeModalVisible && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg p-6 max-w-md w-full\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-between items-center mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-xl font-semibold\",\n                                    children: \"邀请码生成成功\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                    lineNumber: 2113,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"text-gray-500 hover:text-gray-700\",\n                                    onClick: ()=>setIsInviteCodeModalVisible(false),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                        className: \"h-6 w-6\",\n                                        fill: \"none\",\n                                        viewBox: \"0 0 24 24\",\n                                        stroke: \"currentColor\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 2,\n                                            d: \"M6 18L18 6M6 6l12 12\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                            lineNumber: 2119,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                        lineNumber: 2118,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                    lineNumber: 2114,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                            lineNumber: 2112,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between bg-gray-50 p-3 rounded-lg mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"font-mono text-lg text-blue-600 mr-4\",\n                                            children: inviteCode\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                            lineNumber: 2125,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: \"px-3 py-1 bg-blue-500 text-white rounded hover:bg-blue-600\",\n                                            onClick: ()=>{\n                                                navigator.clipboard.writeText(inviteCode).then(()=>alert(\"邀请码已复制\")).catch(()=>alert(\"复制失败，请手动复制\"));\n                                            },\n                                            children: \"复制\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                            lineNumber: 2126,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                    lineNumber: 2124,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-gray-500 text-sm space-y-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: \"您可以:\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                            lineNumber: 2138,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            className: \"list-disc list-inside space-y-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"将邀请码分享给学生，让他们加入班级\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                    lineNumber: 2140,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"邀请其他老师作为协助教师加入班级\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                    lineNumber: 2141,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                            lineNumber: 2139,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-yellow-500\",\n                                            children: \"⏰ 邀请码有效期为24小时\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                            lineNumber: 2143,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                    lineNumber: 2137,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                            lineNumber: 2123,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-end\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: \"px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600\",\n                                onClick: ()=>setIsInviteCodeModalVisible(false),\n                                children: \"关闭\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                lineNumber: 2149,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                            lineNumber: 2148,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                    lineNumber: 2111,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                lineNumber: 2110,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_teacher_space_components_modals__WEBPACK_IMPORTED_MODULE_6__.PublishTaskModal, {\n                visible: isPublishTaskModalVisible,\n                onCancel: ()=>setIsPublishTaskModalVisible(false),\n                onOk: handlePublishTask,\n                students: students,\n                selectedStudents: selectedStudentIds,\n                setSelectedStudents: setSelectedStudentIds,\n                fileList: fileList,\n                uploading: uploading,\n                handleUpload: handleUpload,\n                handleRemoveFile: handleRemoveFile,\n                displayTemplates: templates,\n                officialTemplates: officialTemplates,\n                selectedTemplateId: selectedTemplateId,\n                setSelectedTemplateId: setSelectedTemplateId\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                lineNumber: 2163,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_teacher_space_components_modals__WEBPACK_IMPORTED_MODULE_6__.ResetPasswordModal, {\n                visible: isResetPasswordModalVisible,\n                onCancel: ()=>setIsResetPasswordModalVisible(false),\n                onOk: handleResetPassword,\n                isBatch: false,\n                count: 1\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                lineNumber: 2181,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AssignBlocksModal__WEBPACK_IMPORTED_MODULE_7__.AssignBlocksModal, {\n                visible: isAssignBlocksModalVisible,\n                onCancel: ()=>{\n                    setIsAssignBlocksModalVisible(false);\n                    setSelectedStudentId(null);\n                    setSelectedStudentIds([]);\n                },\n                isClassCardAssign: selectedStudentId !== null,\n                loadingTemplates: loadingTemplates,\n                templates: templates,\n                studentTemplateUsage: studentTemplateUsage,\n                teacherTemplate: teacherTemplate,\n                onSelectTemplate: handleSelectTemplate,\n                onTemplateUsageClick: handleTemplateUsageClick,\n                userId: userId,\n                onRefreshTemplates: fetchTemplates,\n                students: students,\n                selectedStudentIds: selectedStudentIds,\n                userRoles: userRoles,\n                onSuccess: ()=>{\n                    refreshStudentList();\n                    fetchTemplateUsage();\n                },\n                onUpdateStudentTemplate: handleUpdateStudentTemplate\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                lineNumber: 2190,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AssignPointsModal__WEBPACK_IMPORTED_MODULE_9__.AssignPointsModal, {\n                visible: isAssignPointsModalVisible,\n                onCancel: ()=>{\n                    setIsAssignPointsModalVisible(false);\n                    setSelectedStudent(null);\n                },\n                // 根据 selectedStudent 是否存在来决定调用哪个处理函数\n                onOk: selectedStudent ? handleAssignPoints : handleBatchAssignPoints,\n                studentName: selectedStudent ? \"\".concat(selectedStudent.nickName) : \"已选择 \".concat(selectedStudentIds.length, \" 名学生\"),\n                studentId: (selectedStudent === null || selectedStudent === void 0 ? void 0 : selectedStudent.id) || 0,\n                userId: (selectedStudent === null || selectedStudent === void 0 ? void 0 : selectedStudent.userId) || 0,\n                student: selectedStudent,\n                isBatch: !selectedStudent,\n                selectedStudents: selectedStudentIds,\n                students: students,\n                onSuccess: ()=>{\n                // 移除这里的刷新，因为 onOk 内部已经处理了\n                },\n                refreshStudentList: refreshStudentList,\n                onGoToRedeemKey: (studentIds)=>{\n                    console.log(\"前往兑换密钥:\", studentIds);\n                    setIsBatchUseKeyPackageModalVisible(true);\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                lineNumber: 2217,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_BatchUseKeyPackageModal__WEBPACK_IMPORTED_MODULE_10__.BatchUseKeyPackageModal, {\n                open: isBatchUseKeyPackageModalVisible,\n                selectedStudentIds: selectedStudentIds,\n                students: students,\n                onClose: ()=>setIsBatchUseKeyPackageModalVisible(false),\n                onSuccess: handleBatchUseKeyPackageSuccess,\n                onGoToAssignPoints: handleGoToAssignPointsFromRedeem\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                lineNumber: 2243,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n        lineNumber: 1650,\n        columnNumber: 5\n    }, undefined);\n};\n_s(ClassDetail, \"rOoOqxdnOob2DP168DTJvyWgCZI=\", false, function() {\n    return [\n        _contexts_TemplateContext__WEBPACK_IMPORTED_MODULE_4__.useTemplate,\n        react_redux__WEBPACK_IMPORTED_MODULE_13__.useSelector,\n        react_redux__WEBPACK_IMPORTED_MODULE_13__.useSelector\n    ];\n});\n_c = ClassDetail;\n/* harmony default export */ __webpack_exports__[\"default\"] = (ClassDetail);\nvar _c;\n$RefreshReg$(_c, \"ClassDetail\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/workbench/components/ClassDetail.tsx\n"));

/***/ })

});