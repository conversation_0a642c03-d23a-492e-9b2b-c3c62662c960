'use client';

import React, { useState, useEffect } from 'react';
import QuickActions from './QuickActions';
import OngoingTasks from './OngoingTasks';
import TemplateManagement from './TemplateManagement';
import ClassManagement from './ClassManagement';
import ClassDetail from './ClassDetail';
import ClassTasks from './ClassTasks';
import CourseManagement from './CourseManagement';
import ClassProjects from './ClassProjects';
import SchoolSelectionModal from './SchoolSelectionModal';
import ClassSelectionModal from './ClassSelectionModal';
import TemplateSelectionModal from './TemplateSelectionModal';
import { CreateClassModal } from '../../teacher-space/components/modals/create-class-modal';
import { schoolApi } from '@/lib/api/school';
import { classApi } from '@/lib/api/class';
import { Search } from 'lucide-react';
import { message } from 'antd';
import { HandUp } from '@icon-park/react';
import '@icon-park/react/styles/index.css';
import './SchoolSelectionModal.css';
import { useTemplate } from '../contexts/TemplateContext';
import './TemplateSelectionModal.css';
import { updateTeacherClassCache, clearTeacherClassCache } from './LeftSidebar';

interface School {
  id: number;
  schoolName: string;
  province: string;
  city: string;
  district: string;
}

interface ClassInfo {
  id: number;
  schoolId: number;
  grade: string;
  className: string;
  teacherId: number;
  assistantTeacherId: number;
  inviteCode: string;
  createTime: string;
  updateTime: string;
  studentCount: number;
  isAssistant?: boolean;
}

interface MainContentProps {
  activeView?: string;
  selectedSchool?: School | null;
  userInfo?: {
    id: number;
    nickName: string;
    [key: string]: any;
  };
  classes?: ClassInfo[];
  classesLoading?: boolean;
  classesError?: string | null;
  onCloseDropdown?: () => void;
  onClassesUpdate?: (classes: ClassInfo[]) => void; // 新增：班级列表更新回调
  onSchoolChange?: (school: School) => void; // 新增：学校变化回调
}

const MainContent = ({
  activeView = '快速开始',
  selectedSchool,
  userInfo,
  classes = [],
  classesLoading = false,
  classesError = null,
  onCloseDropdown,
  onClassesUpdate,
  onSchoolChange
}: MainContentProps) => {
  const [isSchoolModalOpen, setIsSchoolModalOpen] = useState(false);
  const [isClassModalOpen, setIsClassModalOpen] = useState(false);
  const [isTemplateModalOpen, setIsTemplateModalOpen] = useState(false);
  const [isCreateClassModalOpen, setIsCreateClassModalOpen] = useState(false);
  const [currentActionType, setCurrentActionType] = useState('');
  const [modalSelectedSchool, setModalSelectedSchool] = useState<any>(null);
  const [selectedClass, setSelectedClass] = useState<any>(null);
  const [showClassDetail, setShowClassDetail] = useState(false);
  const [selectedClassForDetail, setSelectedClassForDetail] = useState<any>(null);

  // 使用模板上下文
  const { currentTemplate, globalTemplateChangeVersion } = useTemplate();

  // 监听全局模板变化，通知所有班级详情组件刷新
  useEffect(() => {
    if (globalTemplateChangeVersion > 0 && currentTemplate) {
      console.log('MainContent - 检测到全局模板变化，版本号:', globalTemplateChangeVersion);
      console.log('MainContent - 新的当前模板:', currentTemplate);

      // 这里可以添加通知所有班级组件刷新的逻辑
      // 由于班级详情组件已经在监听globalTemplateChangeVersion，
      // 这里主要是为了确保状态同步
    }
  }, [globalTemplateChangeVersion, currentTemplate]);

  // 监听学校选择变化，强制跳回班级管理页面
  useEffect(() => {
    if (selectedSchool) {
      console.log('检测到学校变化，强制跳回班级管理页面:', selectedSchool);
      // 重置班级详情显示状态，强制显示班级列表
      setShowClassDetail(false);
      setSelectedClassForDetail(null);

      // 通知父组件学校变化
      if (onSchoolChange) {
        onSchoolChange(selectedSchool);
      }
    }
  }, [selectedSchool, onSchoolChange]);

  const handleQuickStartClick = async (actionType: string = '发布任务') => {
    setCurrentActionType(actionType);

    try {
      // 获取用户的学校列表
      const response = await schoolApi.getUserSchools();

      if (response.data.code === 200) {
        const schoolsData = response.data.data || [];

        if (schoolsData.length === 1) {
          // 只有一个学校，直接选择并跳到班级选择
          setModalSelectedSchool(schoolsData[0]);
          setIsClassModalOpen(true);
        } else if (schoolsData.length > 1) {
          // 多个学校，显示学校选择弹窗
          setIsSchoolModalOpen(true);
        } else {
          // 没有学校，可以显示提示信息
          console.warn('用户没有关联的学校');
        }
      }
    } catch (error) {
      console.error('获取学校列表失败:', error);
      // 出错时仍然显示学校选择弹窗
      setIsSchoolModalOpen(true);
    }
  };

  const handleCloseModal = () => {
    setIsSchoolModalOpen(false);
    setIsClassModalOpen(false);
    setIsTemplateModalOpen(false);
    setModalSelectedSchool(null);
    setSelectedClass(null);
    setCurrentActionType('');
  };

  const handleSchoolSelect = (school: any) => {
    setModalSelectedSchool(school);
    setIsSchoolModalOpen(false);
    setIsClassModalOpen(true);
  };

  const handleClassSelect = (classData: any) => {
    setSelectedClass(classData);
    setIsClassModalOpen(false);
    setIsTemplateModalOpen(true);
  };

  const handleBackToSchool = () => {
    setIsClassModalOpen(false);
    setIsSchoolModalOpen(true);
  };

  const handleBackToClass = () => {
    setIsTemplateModalOpen(false);
    setIsClassModalOpen(true);
  };



  const handleClassClick = (classInfo: any) => {
    console.log('点击班级:', classInfo);
    setSelectedClassForDetail(classInfo);
    setShowClassDetail(true);
  };

  const handleBackToClassManagement = () => {
    setShowClassDetail(false);
    setSelectedClassForDetail(null);
  };

  // 处理班级信息更新
  const handleClassInfoUpdate = (updatedClassInfo: ClassInfo) => {
    // 更新班级列表中对应的班级信息
    const updatedClasses = classes.map(classItem =>
      classItem.id === updatedClassInfo.id
        ? { ...classItem, ...updatedClassInfo }
        : classItem
    );

    // 更新缓存
    if (selectedSchool) {
      updateTeacherClassCache(selectedSchool.id, updatedClasses);
    }

    // 通知父组件更新班级列表
    onClassesUpdate?.(updatedClasses);

    // 同时更新当前选中的班级详情
    setSelectedClassForDetail(updatedClassInfo);
  };

  // 处理班级删除
  const handleClassDeleted = (deletedClassId: number) => {
    console.log('班级已删除:', deletedClassId);

    // 从班级列表中移除被删除的班级
    const updatedClasses = classes.filter(cls => cls.id !== deletedClassId);

    // 更新缓存
    if (selectedSchool) {
      updateTeacherClassCache(selectedSchool.id, updatedClasses);
    }

    // 通知父组件更新班级列表
    onClassesUpdate?.(updatedClasses);
  };

  // 处理添加班级
  const handleAddClass = () => {
    if (!selectedSchool) {
      message.error('请先选择学校');
      return;
    }

    setIsCreateClassModalOpen(true);
  };

  // 处理模板选择确认
  const handleTemplateConfirm = (taskData: any) => {
    console.log('模板选择确认:', taskData);
    // 简单关闭弹窗，不执行具体的发布逻辑
    handleCloseModal();
  };

  // 处理创建班级表单提交
  const handleCreateClass = async (values: { className: string }) => {
    if (!selectedSchool) {
      message.error('请先选择学校');
      return;
    }

    if (!userInfo?.id) {
      message.error('用户信息不完整，请重新登录');
      return;
    }

    if (values.className.length > 8) {
      message.error('班级名称不能超过8个字符');
      return;
    }

    try {
      // 使用 createClass API，需要传递 teacherId
      const response = await classApi.createClass(selectedSchool.id, values.className, userInfo.id);

      if (response.data.code === 200) {
        message.success('创建班级成功');
        setIsCreateClassModalOpen(false);

        // 刷新班级列表并更新缓存
        try {
          const classesResponse = await classApi.getTeacherClassesSimple(selectedSchool.id);
          if (classesResponse.data.code === 200) {
            const updatedClasses = classesResponse.data.data || [];
            // 更新缓存
            updateTeacherClassCache(selectedSchool.id, updatedClasses);
            // 更新UI
            onClassesUpdate?.(updatedClasses);
          }
        } catch (error) {
          console.error('刷新班级列表失败:', error);
        }
      } else {
        message.error(response.data.message || '该班级已存在或创建失败');
      }
    } catch (error: any) {
      console.error('创建班级失败:', error);
      message.error(error.response?.data?.message || '创建班级失败');
    }
  };

  // 处理创建班级弹窗关闭
  const handleCreateClassModalClose = () => {
    setIsCreateClassModalOpen(false);
  };

  return (
    <main className="main-content relative">
      <header className="main-header">
        <div className="search-bar">
          <Search className="search-icon" size={18} />
          <input type="text" placeholder="搜索课程、任务或学生..." />
        </div>
        <button className="start-class-btn" onClick={() => handleQuickStartClick('快速上课')}>
          <HandUp theme="filled" size={20} fill={["#ffffff"]} className="start-class-icon" />
          <span>快速上课</span>
        </button>
      </header>
      <div className="content-area"> {/* 根据activeView渲染不同内容 */}
      {activeView === '模板管理' ? (
        <TemplateManagement selectedSchool={selectedSchool} userInfo={userInfo} />
      ) : activeView === '班级管理' ? (
        showClassDetail && selectedClassForDetail && selectedSchool ? (
          <ClassDetail
            classInfo={selectedClassForDetail}
            selectedSchool={selectedSchool}
            onBack={handleBackToClassManagement}
            onClassInfoUpdate={handleClassInfoUpdate}
            onClassDeleted={handleClassDeleted}
          />
        ) : (
          <ClassManagement
            selectedSchool={selectedSchool}
            userInfo={userInfo}
            classes={classes}
            classesLoading={classesLoading}
            classesError={classesError}
            onClassClick={handleClassClick}
            onCloseDropdown={onCloseDropdown}
            onAddClass={handleAddClass}
          />
        )
      ) : activeView === '班级任务' ? (
        <ClassTasks />
      ) : activeView === '课程管理' ? (
        <CourseManagement />
      ) : activeView === '班级项目' ? (
        <ClassProjects />
      ) : (
        <>
          <QuickActions />
          <OngoingTasks />
        </>
      )}

      <SchoolSelectionModal
        isOpen={isSchoolModalOpen}
        onClose={handleCloseModal}
        actionType={currentActionType}
        onSchoolSelect={handleSchoolSelect}
      />

      <ClassSelectionModal
        isOpen={isClassModalOpen}
        onClose={handleCloseModal}
        onBack={handleBackToSchool}
        actionType={currentActionType}
        selectedSchool={modalSelectedSchool}
        onClassSelect={handleClassSelect}
      />

      <TemplateSelectionModal
        isOpen={isTemplateModalOpen}
        onClose={handleCloseModal}
        onBack={handleBackToClass}
        onConfirm={handleTemplateConfirm}
        actionType={currentActionType}
        selectedSchool={modalSelectedSchool}
        selectedClass={selectedClass}
      />

      <CreateClassModal
        visible={isCreateClassModalOpen}
        onCancel={handleCreateClassModalClose}
        onOk={handleCreateClass}
      /></div>
    </main>
  );
};

export default MainContent; 